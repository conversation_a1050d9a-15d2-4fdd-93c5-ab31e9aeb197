:root {
  /* Light theme (default) */
  --bg-primary: #FFFFFF;
  --bg-secondary: #FAFAFA;
  --border-color: #E9EBF2;
  --text-primary: #606F95;
  --text-accent: #1B1D21;
  --btn-hover: #FAFAFA;
  --btn-border: #DCE0E5;

  /* Action Button Variables */
  --action-btn-bg: #470CED;
  --action-btn-hover: #2A00A0;
  --action-btn-disabled: #cfd4d4;
  --action-btn-text: #FFFFFF;
  
  /* Gradient colors */
  --color-1: 27 92% 53%;    /* Orange: #f77f16 */
  --color-2: 285 96% 50%;   /* Purple: #ab05f9 */
  --color-3: 183 85% 50%;   /* Turquoise: #11e6ec */
  --color-4: 90 3% 93%;     /* Light Gray: #eeefed */

  /* Loaded Files UI Variables */
  --loaded-files-border: #04AE2C;
  --loaded-files-text: #606F95;
  --loaded-files-counter-bg: rgba(4, 174, 44, 0.1);
  --loaded-files-counter-text: #04AE2C;
  --clear-button-bg: rgba(250, 88, 58, 0.05);
  --clear-button-icon: #FF391F;
  --loaded-files-progress-bg: rgba(96, 111, 149, 0.1);
  --loaded-files-progress-fill: #04AE2C;

  /* Add transition properties at root level */
  --theme-transition: background-color 0.3s ease,
                     color 0.3s ease,
                     border-color 0.3s ease,
                     fill 0.3s ease,
                     stroke 0.3s ease,
                     opacity 0.3s ease,
                     box-shadow 0.3s ease;

  /* Tooltip variables */
  --tooltip-bg: #000000;
  --tooltip-text: #FFFFFF;
  --tooltip-font-size: 12px;
  --tooltip-padding: 8px 16px;
  --tooltip-radius: 6px;
  --tooltip-arrow-size: 5px;
  --tooltip-transition: opacity 0.2s ease, visibility 0.2s ease;
}

[data-theme="dark"] {
  /* Softer Dark Theme 2024 */
  --bg-primary: #1E2028;      /* Soft dark blue-gray */
  --bg-secondary: #252832;    /* Slightly lighter background for cards */
  --border-color: #2F3341;    /* Subtle blue-tinted borders */
  --text-primary: #B4B9C5;    /* Soft white-gray for regular text */
  --text-accent: #FFFFFF;     /* Pure white for headings */
  --btn-hover: #292E38;       /* Sidebar button hover color */
  --btn-border: #383C4A;      /* Slightly lighter for buttons */
  
  /* Action Button Dark Theme */
  --action-btn-disabled: #2F353D;
  
  /* Modern Gradient Colors - Softer Tones */
  --color-1: 250 70% 60%;     /* Soft Purple-Blue: #7B8CFF */
  --color-2: 200 75% 60%;     /* Ocean Blue: #47A3FF */
  --color-3: 170 65% 60%;     /* Soft Teal: #4ECBC4 */
  --color-4: 220 15% 23%;     /* Blue-Gray: #323645 */

  /* Loaded Files UI Dark Theme */
  --loaded-files-border: #04AE2C;
  --loaded-files-text: #FFFFFF;
  --loaded-files-counter-bg: rgba(4, 174, 44, 0.1);
  --loaded-files-counter-text: #04AE2C;
  --clear-button-bg: rgba(250, 88, 58, 0.1);
  --clear-button-icon: #FF391F;
  --loaded-files-progress-bg: rgba(255, 255, 255, 0.1);
  --loaded-files-progress-fill: #04AE2C;

  /* Tooltip Dark Theme */
  --tooltip-bg: #000000;
  --tooltip-text: #FFFFFF;
}

/* Remove transitions from specific elements where needed */
.sidebar-collapse-btn .collapse-icon,
.sidebar.collapsed * {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure smooth transitions for background images and filters */
img {
  transition: var(--theme-transition);
}

/* Add specific transitions for elements that need them */
.sidebar-btn,
.theme-toggle-btn,
.progress-bar,
.metric-item {
  transition: var(--theme-transition);
}

@font-face {
  font-family: 'Amazon Ember';
  src: url('fonts/AmazonEmber_Regular.woff2') format('woff2'),
       url('fonts/AmazonEmber_Regular.woff') format('woff'),
       url('fonts/AmazonEmber_Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Amazon Ember';
  src: url('fonts/Amazon-Ember-Medium.woff2') format('woff2'),
       url('fonts/Amazon-Ember-Medium.woff') format('woff'),
       url('fonts/Amazon-Ember-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Amazon Ember';
  src: url('fonts/AmazonEmber_Bold.woff2') format('woff2'),
       url('fonts/AmazonEmber_Bold.woff') format('woff'),
       url('fonts/AmazonEmber_Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Amazon Ember';
  src: url('fonts/AmazonEmber_He.woff2') format('woff2'),
       url('fonts/AmazonEmber_He.woff') format('woff'),
       url('fonts/AmazonEmber_He.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Global Styles */
body,
.sidebar,
.sidebar-logo,
.sidebar-nav,
.sidebar-btn,
.main-content {
  font-family: 'Amazon Ember', Arial, sans-serif !important;
}

body {
  margin: 0;
  background: var(--bg-secondary);
  color: var(--text-primary);
  min-width: 1024px;
  overflow-x: auto !important;
  overflow-y: hidden !important;
}

.snapapp-container {
  display: flex;
  height: 100vh; /* Use viewport height instead of min-height */
  min-width: 1024px;
  width: 100%;
  overflow: visible !important;
}

/* Sidebar Styles */
.sidebar {
  width: 310px;
  min-width: 310px;
  background: var(--bg-primary);
  border-right: 1.5px solid var(--border-color);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 40px;
  position: fixed; /* Fix the sidebar */
  top: 0;
  left: 0;
  bottom: 0;
  box-sizing: border-box;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 200; /* Increased from 50 to ensure sidebar stays above main content */
}

.sidebar-inner {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  overflow: hidden;
  align-items: flex-start;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-logo {
  width: 230px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 80px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Make wordmark white in dark mode */
[data-theme="dark"] .sidebar-logo img[alt="Snap for MOD Logo"] {
  filter: brightness(0) invert(1);
}

.sidebar-logo-icon {
  width: 48px !important;
  height: 48px !important;
  margin-right: 0;
  vertical-align: middle;
}

.sidebar-nav {
  width: 230px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1 1 auto;
  align-items: flex-start;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-btn {
  width: 100%;
  height: 56px;
  display: flex;
  align-items: center;
  padding: 0 28px 0 16px;
  border-radius: 14px;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
  border: 2px solid transparent;
  background: transparent;
  box-sizing: border-box;
  position: relative;
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  justify-content: flex-start;
}

.sidebar-btn .sidebar-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  flex-shrink: 0;
  transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-btn span {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-btn:hover:not(.active):not(.locked):not(:disabled) {
  background: var(--btn-hover);
  color: var(--text-accent);
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Active state - expanded */
.sidebar-btn.active {
  color: var(--text-accent);
  font-weight: 500;
  border: 2px solid var(--btn-border);
}

/* Dark mode active state */
[data-theme="dark"] .sidebar-btn.active {
  border-color: var(--btn-border);
  background: transparent !important;
}

/* Collapsed state base */
.sidebar.collapsed .sidebar-btn {
  width: 60px;
  padding: 0 16px;
  justify-content: flex-start;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Collapsed state active */
.sidebar.collapsed .sidebar-btn.active {
  width: 60px;
  padding: 0 16px;
  border: 2px solid var(--btn-border);
}

[data-theme="dark"] .sidebar.collapsed .sidebar-btn.active {
  border-color: var(--btn-border);
}

/* Hide text in collapsed state */
.sidebar.collapsed .sidebar-btn span {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity;
}

/* Remove margin from icon in collapsed state */
.sidebar.collapsed .sidebar-btn .sidebar-icon {
  margin-right: 0;
  transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Transitions only for non-layout properties */
.sidebar-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar {
  /* Removed transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); */
}

.sidebar-btn span {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-btn {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Removed background-color, color, and border-color transitions to prevent flashing on theme change */
}

.sidebar-btn.locked {
  opacity: 1;
  cursor: not-allowed;
  background: none;
  color: #E9EBF2;
  font-weight: 500;
  border: none;
}

.sidebar-btn.active .sidebar-icon {
  /* Using dedicated active state icon instead of filter */
}

.sidebar-btn:hover:not(.active) .sidebar-icon {
  filter: none !important;
}

/* Add styles for disabled sidebar buttons */
.sidebar-btn:disabled {
  cursor: not-allowed;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}

.sidebar-btn:disabled span,
.sidebar-btn.locked span,
.sidebar-btn:disabled .sidebar-icon,
.sidebar-btn.locked .sidebar-icon {
  color: #606F95 !important;
  opacity: 0.45;
}

[data-theme="dark"] .sidebar-btn:disabled span,
[data-theme="dark"] .sidebar-btn.locked span,
[data-theme="dark"] .sidebar-btn:disabled .sidebar-icon,
[data-theme="dark"] .sidebar-btn.locked .sidebar-icon {
  color: #606F95 !important;
  opacity: 0.45;
}

/* Remove tooltip for disabled buttons */
.sidebar-btn:disabled::after {
  display: none !important;
}

.sidebar.collapsed .sidebar-btn:disabled::after {
  display: none !important;
}

/* Locked button styles */
.sidebar-btn.locked {
  position: relative;
  opacity: 1;
  cursor: not-allowed;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Make lock icon container fully visible */
.lock-icon-container {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #FF391F;
  border-radius: 12px;
  z-index: 2;
  filter: none !important;
  opacity: 1 !important;
  padding: 0;
  box-sizing: border-box;
}

/* The lock icon itself */
.lock-icon {
  width: 14px;
  height: 14px;
  color: #FFFFFF;
  display: block; /* Remove any potential inline spacing */
  margin: 0; /* Remove any margins */
}

/* Apply a CSS filter hack to counteract parent opacity */
.sidebar-btn.locked .lock-icon-container {
  filter: contrast(1.5) brightness(1.3) !important; /* Make it fully visible */
  opacity: 1 !important;
  z-index: 4; /* Above the tooltip */
}

/* Remove old tooltip styles */
.sidebar-btn.locked::before,
.sidebar-btn.locked::after {
  display: none;
}

/* Add new tooltip styles */
.tooltip {
  /* Force tooltip to be single-line */
  white-space: nowrap;
  width: auto;
  max-width: none;
  min-width: unset;
  position: absolute;
  top: 50%;
  left: calc(100% + 12px);
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  background: #FF391F;
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  pointer-events: none;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  gap: 8px;
}

.tooltip img {
  width: 14px;
  height: 14px;
}

.sidebar-btn.locked:hover .tooltip {
  opacity: 1;
}

/* Arrow for tooltip */
.tooltip::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 100%;
  transform: translateY(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: transparent #FF391F transparent transparent;
  margin: 0;
  width: 0;
  height: 0;
}

/* Position tooltip for collapsed sidebar */
.sidebar.collapsed .tooltip {
  left: calc(100% + 24px);
}

/* Position arrow for collapsed sidebar */
.sidebar.collapsed .tooltip::after {
  right: 100%;
  margin-right: 0;
}

.sidebar-collapse-btn {
  position: absolute;
  right: -16px;
  top: 47px;
  width: 32px;
  height: 32px;
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  padding: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode collapse button */
[data-theme="dark"] .sidebar-collapse-btn {
  background: #1A1D21;
  border-color: var(--border-color);
  box-shadow: none;
}

/* Hover state for both collapsed and uncollapsed */
.sidebar-collapse-btn:hover {
  border-color: var(--border-color);
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.08);
}

/* Dark mode hover state */
[data-theme="dark"] .sidebar-collapse-btn:hover {
  border-color: var(--border-color);
  box-shadow: none;
}

/* Collapse icon with smooth transition */
.collapse-icon {
  width: 24px;
  height: 24px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
  transform-origin: center;
}

/* Make collapse icon white in dark mode */
[data-theme="dark"] .collapse-icon {
  filter: brightness(0) invert(1);
}

/* Base transform for uncollapsed hover */
.sidebar-collapse-btn:hover .collapse-icon {
  transform: scale(1.08);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base transform for collapsed state */
.sidebar.collapsed .collapse-icon {
  transform: rotate(180deg);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Combined transform for collapsed hover */
.sidebar.collapsed .sidebar-collapse-btn:hover .collapse-icon {
  transform: rotate(180deg) scale(1.08);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed {
  width: 140px;
  min-width: 140px;
  padding: 40px;
  align-items: flex-start;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1), min-width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Removed background/color transitions to prevent flashing on theme change */
}

.sidebar.collapsed .sidebar-inner {
  width: 60px;
  align-items: flex-start;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed .sidebar-logo {
  width: 60px;
  min-width: 60px;
  margin-bottom: 80px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1), min-width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Removed background/color transitions to prevent flashing on theme change */
}

.sidebar.collapsed .sidebar-logo img:not(:first-child) {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed .sidebar-btn::after {
  content: attr(data-label);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: #222;
  color: #fff;
  font-size: 13px;
  font-weight: 400;
  padding: 4px 10px;
  border-radius: 6px;
  opacity: 0;
  pointer-events: none;
  white-space: nowrap;
  margin-left: 8px;
  z-index: 10;
  transition: opacity 0.2s;
}

.sidebar.collapsed .sidebar-btn:hover::after {
  opacity: 1;
}

/* Dark mode styles for collapsed sidebar button tooltips */
[data-theme="dark"] .sidebar.collapsed .sidebar-btn::after {
  background: #FFFFFF;
  color: #1E2028;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.sidebar.collapsed .sidebar-nav {
  width: 60px;
  min-width: 60px;
  align-items: flex-start;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1), min-width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Removed background/color transitions to prevent flashing on theme change */
}

.sidebar.collapsed .sidebar-btn,
.sidebar.collapsed .sidebar-btn.active,
.sidebar.collapsed .sidebar-btn:hover {
  width: 60px;
  padding: 0 16px;
  justify-content: flex-start;
  margin: 0;
}

.sidebar.collapsed .sidebar-btn span {
  display: none !important;
}

.sidebar-icon {
  width: 28px;
  height: 28px;
  margin-right: 12px;
  vertical-align: middle;
}

/* Main content styles */
.main-content {
  flex: 1;
  padding: 40px 30px 40px 40px; /* Adjusted right padding to compensate for 10px scrollbar */
  display: flex;
  flex-direction: column;
  gap: 32px;
  width: 100%;
  box-sizing: border-box;
  min-width: 1024px;
  margin-left: 310px; /* Add margin equal to sidebar width */
  height: 100vh;
  overflow-x: visible !important;
  overflow-y: auto; /* Enable scrolling for main content */
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 10; /* Reduced from 100 to ensure main content stays below sidebar */
}

/* Add global header styles */
.main-content h1 {
  font-size: 24px;
  margin: 0;
  margin-top: 8px;
  font-weight: 500;
  color: var(--text-accent);
}

/* Remove max-width constraints */
@media (min-width: 1024px) {
  .sidebar {
    width: 310px;
    padding: 40px;
  }
  .sidebar-logo {
    margin-bottom: 80px;
  }
}

@media (min-width: 1440px) {
  .main-content {
    padding: 40px 30px 40px 40px; /* Adjusted right padding to compensate for 10px scrollbar */
    width: 100%;
  }
}

/* Theme toggle styles */
.theme-toggle-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: auto;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-toggle-btn {
  width: 100px;
  height: 43px;
  background: #E8EBF4;
  border-radius: 28.82px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 2.88px;
  position: relative;
  gap: 2.88px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

[data-theme="dark"] .theme-toggle-btn {
  background: #292E38;
}

.theme-toggle-btn .toggle {
  width: 45.68px;
  height: 37.24px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  border-radius: 23.05px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  left: 2.88px;
}

.theme-toggle-btn .toggle:last-child {
  left: 51.44px;
}

.theme-toggle-btn .toggle.active {
  background: #FFFFFF;
  z-index: 2;
}

[data-theme="dark"] .theme-toggle-btn .toggle.active {
  background: #1B1D21;
}

/* Collapsed state - Keep transitions for collapse/expand */
.sidebar.collapsed .theme-toggle-container {
  width: 60px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed .theme-toggle-btn {
  width: 43px;
  height: 43px;
  padding: 2.88px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed .theme-toggle-btn .toggle {
  width: 37.24px;
  height: 37.24px;
  left: 2.88px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed .toggle:not(.active) {
  opacity: 0;
  visibility: hidden;
  transform: translateX(-100%);
}

.sidebar.collapsed .toggle.active {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

/* Remove transitions only for theme switching in collapsed state */
[data-theme="dark"] .sidebar.collapsed .theme-toggle-btn,
[data-theme="light"] .sidebar.collapsed .theme-toggle-btn {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] .sidebar.collapsed .toggle,
[data-theme="light"] .sidebar.collapsed .toggle {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] .sidebar.collapsed .toggle.active,
[data-theme="light"] .sidebar.collapsed .toggle.active {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] .sidebar.collapsed .toggle:not(.active),
[data-theme="light"] .sidebar.collapsed .toggle:not(.active) {
  transition: none;
}

.theme-icon {
  width: 16px;
  height: 16px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-icon img {
  width: 16px;
  height: 16px;
  color: #384459;
}

/* Make all icons white in dark mode */
[data-theme="dark"] .theme-toggle-btn .theme-icon img {
  color: #FFFFFF;
  filter: brightness(0) invert(1);
}

/* Ensure icons in active toggle are properly colored */
[data-theme="dark"] .theme-toggle-btn .toggle.active .theme-icon img {
  color: #FFFFFF;
  filter: brightness(0) invert(1);
}

/* Global utility classes */
img {
  user-drag: none;
  -webkit-user-drag: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: auto;
}

/* Position lock icon in collapsed mode */
.sidebar.collapsed .lock-icon-container {
  right: 0;
  top: auto;
  bottom: 0;
  transform: none;
  z-index: 4;
}

/* Adjust margin when sidebar is collapsed */
.sidebar.collapsed + .main-content,
.sidebar.collapsed ~ .main-content {
  margin-left: 140px; /* Adjust margin when sidebar is collapsed */
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Action Button */
.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 42px;
  padding: 0 24px;
  border: none;
  border-radius: 6px;
  background: var(--action-btn-bg);
  cursor: pointer;
  transition: var(--theme-transition);
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--action-btn-text);
}

.action-button:hover:not(:disabled) {
  background: var(--action-btn-hover);
}

.action-button:disabled {
  background: var(--action-btn-disabled);
  opacity: 0.8;
  cursor: not-allowed;
}

.action-button img {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
  transition: var(--theme-transition);
}

.action-button:disabled img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
  opacity: 0.8;
}

/* Full width variant */
.action-button.full-width {
  width: 100%;
}

/* Global Tooltip Styles */
[data-tooltip] {
  position: relative;
}

[data-tooltip]:before {
  /* Force tooltip to be single-line and auto-size properly */
  white-space: nowrap !important;
  width: auto !important;
  max-width: none !important;
  min-width: auto !important;
  content: attr(data-tooltip);
  position: fixed !important;
  bottom: var(--tooltip-bottom, calc(100% + 8px));
  left: var(--tooltip-left, 50%);
  transform: translateX(-50%);
  padding: var(--tooltip-padding) !important;
  background: var(--tooltip-bg) !important;
  color: var(--tooltip-text) !important;
  font-family: 'Amazon Ember', sans-serif;
  font-size: var(--tooltip-font-size) !important;
  font-weight: 500;
  border-radius: var(--tooltip-radius) !important;
  line-height: 1.4; /* Add more vertical room */
  opacity: 1 !important;
  visibility: hidden;
  transition: var(--tooltip-transition);
  z-index: 999999 !important;
  pointer-events: none;
  /* Ensure proper text measurement */
  display: inline-block !important;
  text-align: center !important;
}



[data-tooltip]:after {
  content: '';
  position: fixed !important;
  bottom: var(--tooltip-bottom, calc(100% + 4px));
  left: var(--tooltip-left, 50%);
  transform: translateX(-50%);
  border-left: var(--tooltip-arrow-size, 5px) solid transparent;
  border-right: var(--tooltip-arrow-size, 5px) solid transparent;
  border-top: var(--tooltip-arrow-size, 5px) solid var(--tooltip-bg);
  opacity: 1 !important;
  visibility: hidden;
  transition: var(--tooltip-transition);
  z-index: 999999 !important;
  pointer-events: none;
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
  opacity: 1 !important;
  visibility: visible;
}

/* Dark theme adjustments */
[data-theme="dark"] [data-tooltip]:before {
  background: var(--tooltip-bg) !important;
  color: var(--tooltip-text) !important;
}

[data-theme="dark"] [data-tooltip]:after {
  border-top-color: var(--tooltip-bg) !important;
}

/* Ensure parent containers don't mask tooltips */
[data-tooltip] {
  overflow: visible !important;
}

/* Fix tooltips showing with text cursor instead of pointer */
input[data-tooltip],
textarea[data-tooltip] {
  cursor: pointer !important;
}

/* Prevent tooltip clipping by parent containers (but preserve scroll containers) */
.listing-analytics-div,
.listing-middle-div,
.listing-badges-row,
.listing-badge,
.listing-ad-row,
.listing-edit-analyse-row,
.listing-analyse-ic,
.listing-edit-ic,
.marketplaces-div,
.marketplace-col,
.sales-cards-container,
.dashboard-component,
.account-status,
.listings-status,
.ad-spend,
.database-container {
  overflow: visible !important;
}

/* Allow main-content to scroll vertically but not clip tooltips horizontally */
.main-content {
  overflow-x: visible !important;
  overflow-y: auto !important;
}

/* Allow Sales-card-div to scroll vertically but not clip tooltips horizontally */
.Sales-card-div {
  overflow-x: visible !important;
  overflow-y: auto !important;
}

/* Force tooltips to be on top of everything */
.fit-type-tooltip,
.ad-spend-tooltip,
.ordered-colors-tooltip,
[data-tooltip]:before,
[data-tooltip]:after {
  z-index: 999999 !important;
  position: fixed !important;
}

/* Ensure parent containers don't interfere with tooltip sizing */
.listing-edit-analyse-row,
.listing-analyse-ic,
.listing-edit-ic {
  overflow: visible !important;
  position: relative !important;
}

.sidebar-btn.locked span {
  opacity: 0.5 !important;
}
[data-theme="dark"] .sidebar-btn.locked span {
  opacity: 0.5 !important;
}

.sidebar:not(.collapsed) .sidebar-btn span {
  opacity: 1;
  pointer-events: auto;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline;
}

.sidebar-logo img.wordmark {
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.sidebar.collapsed .sidebar-logo img.wordmark {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Tip Card Styles */
.main-content {
  position: relative;
}
.tip-card {
  position: static;
  left: auto;
  right: auto;
  bottom: auto;
  margin-top: auto;
  margin-bottom: 0;
  background: var(--bg-primary);
  border-radius: 14px;
  padding: 32px;
  display: flex;
  flex-direction: column;
  gap: 32px;
  max-width: 460px;
  width: auto;
  box-sizing: border-box;
  margin-left: 0;
  margin-right: 0;
  border: 1.5px solid var(--border-color);
  z-index: 10;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
}

.tip-card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.tip-card-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 18px;
  line-height: 1.2em;
  color: var(--text-accent);
}

.tip-card-description {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4em;
  color: var(--text-primary);
}

.tip-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* Use our existing checkbox styles */
.tip-card .checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.tip-card .checkbox-wrapper img {
  width: var(--checkbox-size);
  height: var(--checkbox-size);
}

/* Global checkbox styling for unchecked state */
.checkbox-wrapper img[src*="uncheckedbox-ic"] {
  opacity: 0.2;
}

[data-theme="dark"] .checkbox-wrapper img[src*="uncheckedbox-ic"] {
  opacity: 1;
}

/* Compare checkbox styling for unchecked state */
.compare-checkbox img[src*="uncheckedbox-ic"] {
  opacity: 0.2;
}

[data-theme="dark"] .compare-checkbox img[src*="uncheckedbox-ic"] {
  opacity: 1;
}

.tip-card .checkbox-wrapper label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.67em;
  color: var(--text-primary);
  cursor: pointer;
}

.tip-card-navigation {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Figma-style navigation buttons */
.tip-card-navigation button {
  height: 36px;
  padding: 0 24px;
  border-radius: 6px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tip-card-navigation button.prev {
  background: transparent;
  border: 1px solid var(--btn-border);
  color: var(--text-primary);
}

.tip-card-navigation button.next,
.tip-card-navigation button.done {
  background: var(--action-btn-bg);
  border: none;
  color: var(--action-btn-text);
}

.tip-card-navigation button.prev:hover {
  border-color: var(--btn-border);
  background: var(--btn-hover);
}

.tip-card-navigation button.next:hover,
.tip-card-navigation button.done:hover {
  background: var(--action-btn-hover);
}

/* Dark theme overrides */
[data-theme="dark"] .tip-card {
  box-shadow: none;
}

@media (max-width: 600px) {
  .tip-card {
    left: var(--main-content-padding-mobile, 16px);
    right: var(--main-content-padding-mobile, 16px);
    bottom: var(--main-content-padding-mobile, 16px);
    padding: 16px;
    max-width: 100%;
  }
}

/* Dashboard Component Styles */
.dashboard-component {
  padding: 0 0 20px 0;
  width: 100%;
  min-width: 0; /* Prevent flex item from overflowing */
}

.account-status {
  width: 100%;
  min-height: 92px;
  background: #FFFFFF;
  border: 1.5px solid #E9EBF2;
  border-radius: 14px;
  padding: 24px;
  margin-top: 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.account-status-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 30px;
  margin-bottom: 32px;
  width: 100%;
  min-width: 0;
}

.account-status-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.account-status-title span {
  display: flex;
  align-items: center;
  height: 16px;
  line-height: 16px;
  color: #606F95;
  margin-top: 2px;
}

.account-status-icon {
  width: 16px;
  height: 16px;
}

.account-status-title span {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 10px;
}

.account-status-metrics {
  display: grid;
  grid-template-columns: auto 1fr 1fr 1fr;
  gap: 24px;
  width: 100%;
  min-width: 0;
}

.account-status-tier {
  position: relative;
  min-width: 0;
  height: 100%;
  padding-left: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.tier-info {
  display: flex;
  flex-direction: column;
}

.tier-label {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 10px;
}

.tier-value {
  color: var(--text-accent);
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 800;
  font-size: 24px;
  line-height: 1.2;
  margin-top: 10px;
  position: relative;
  z-index: 1;
  padding-bottom: 0.1em;
}

.metric-item {
  position: relative;
  min-width: 0;
  height: 100%;
  padding-left: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 25%;
  width: 1px;
  height: 50%;
  background: #606F95;
  opacity: 0.1;
  transition: var(--theme-transition);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  order: 1;
}

.metric-label {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 10px;
}

.metric-percentage {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 8px;
}

.progress-bar {
  position: relative;
  height: 6px !important; /* Force height to be consistent */
  min-height: 6px; /* Ensure minimum height */
  max-height: 6px; /* Ensure maximum height */
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  margin: 0;
  order: 2;
}

.progress-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #F7F8FA;
  border-radius: 10px;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: var(--progress-percentage, 89%);
  background: var(--loaded-files-progress-fill);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.metric-subtext {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  margin-top: 4px;
  order: 3;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 4px;
}

.metric-remaining {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.4;
  margin-left: auto;
  position: relative;
  flex-shrink: 0;
}

[data-theme="dark"] .account-status {
  background: var(--bg-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .progress-track {
  background: var(--border-color);
}

[data-theme="dark"] .progress-fill {
  background: var(--loaded-files-progress-fill);
}

[data-theme="dark"] .metric-item:not(:first-child)::before,
[data-theme="dark"] .metric-item::before {
  background: #B4B9C5;
  opacity: 0.1;
}

[data-theme="dark"] .metric-label,
[data-theme="dark"] .metric-percentage,
[data-theme="dark"] .metric-subtext,
[data-theme="dark"] .metric-remaining,
[data-theme="dark"] .tier-label,
[data-theme="dark"] .account-status-title span {
  color: #FFFFFF;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
}

.gradient-text {
  position: relative;
  display: inline-block;
  color: transparent;
  background: linear-gradient(
    45deg,
    #ff00fa 0%,
    #ff00fa 15%,
    #fc084e 35%,
    #ff9100 65%,
    #ff00fa 85%,
    #ff00fa 100%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  animation: gradient-shift 8s ease-in-out infinite alternate;
  padding: 0.3em 0;
  margin: 0;
  line-height: 1.2;
  height: auto;
  overflow: visible;
  border-radius: 0;
}

@media (min-width: 1024px) {
  .metric-item {
    padding-left: 24px;
  }
}

/* Listings Status Styles (Figma-inspired) */
.listings-status {
  width: 100%;
  min-height: 92px;
  background: #FFFFFF;
  border: 1.5px solid #E9EBF2;
  border-radius: 14px;
  padding: 24px;
  margin-top: 32px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.listings-status-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 30px;
  margin-bottom: 32px;
  width: 100%;
  min-width: 0;
}

.listings-status-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.listings-status-icon {
  width: 16px;
  height: 16px;
}

.listings-status-title span {
  color: #470CED;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 10px;
}

.listings-status-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  width: 100%;
  min-width: 0;
}

.listings-metric-item {
  position: relative;
  min-width: 0;
  height: 100%;
  padding-left: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: none;
}

.listings-metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.listings-metric-label {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 10px;
}

.listings-metric-value {
  color: var(--text-accent);
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 800;
  font-size: 18px;
  line-height: 1.2;
}

.listings-metric-subtext {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 8px;
  margin: 0;
  margin-top: 4px;
}

[data-theme="dark"] .listings-status {
  background: var(--bg-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .listings-metric-label,
[data-theme="dark"] .listings-metric-value,
[data-theme="dark"] .listings-metric-subtext,
[data-theme="dark"] .listings-status-title span {
  color: #FFFFFF;
}

@media (max-width: 900px) {
  .listings-status-metrics {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
}

@media (max-width: 600px) {
  .listings-status {
    padding: 12px;
  }
  .listings-status-header {
    margin-bottom: 16px;
  }
  .listings-status-metrics {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* Listings Status Overview (Figma-accurate) */
.listings-status-overview {
  width: 100%;
  background: #fff;
  border: 1.5px solid #E9EBF2;
  border-radius: 14px;
  padding: 24px;
  margin-top: 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.listings-status-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.listing-status-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  box-sizing: border-box;
  width: auto;
  gap: 4px;
}

.listing-status-top {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.listing-status-number {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: 1.2;
  color: #606F95;
}
.listing-status-number.live {
  color: #04AE2C;
}
.listing-status-number.rejected {
  color: #FF391F;
}

.listing-status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 12px;
}

.listing-status-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.2;
  color: #606F95;
  margin-top: 2px;
  letter-spacing: 0.02em;
}

.listing-status-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1px;
  height: 35px;
  background: #606F95;
  opacity: 0.1;
  margin: 0 8px;
}

[data-theme="dark"] .listings-status-overview {
  background: var(--bg-primary);
  border-color: var(--border-color);
}
[data-theme="dark"] .listing-status-number,
[data-theme="dark"] .listing-status-label {
  color: #B4B9C5;
}
[data-theme="dark"] .listing-status-number.live {
  color: #04AE2C;
}
[data-theme="dark"] .listing-status-number.rejected {
  color: #FF391F;
}
[data-theme="dark"] .listing-status-divider svg rect {
  fill: #B4B9C5;
  fill-opacity: 0.1;
}

@media (max-width: 900px) {
  .listings-status-row {
    flex-wrap: wrap;
    gap: 8px;
  }
  .listing-status-card {
    min-width: 120px;
    padding: 8px 8px;
  }
}
@media (max-width: 600px) {
  .listings-status-overview {
    padding: 12px;
  }
  .listings-status-row {
    flex-direction: column;
    gap: 0;
  }
  .listing-status-card {
    flex: none;
    min-width: 0;
    width: 100%;
    padding: 8px 0;
    border-bottom: 1px solid #E9EBF2;
  }
  .listing-status-card:last-child {
    border-bottom: none;
  }
  .listing-status-divider {
    display: none;
  }
}

.snap-dropdown {
    position: relative;
    min-width: 216px;
    width: 216px;
    cursor: pointer;
    user-select: none;
}
.snap-dropdown .dropdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    height: 40px;
    border: 1.5px solid #DCE0E5;
    border-radius: 4px;
    background: white;
    box-sizing: border-box;
}
.snap-dropdown.focused .dropdown-header {
    border-color: #470CED;
}
.snap-dropdown .dropdown-header span {
    padding-left: 12px;
}
.snap-dropdown .dropdown-header img {
    margin-right: 12px;
}
.snap-dropdown .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: #FFFFFF;
    border: 1.5px solid #E9EBF2;
    border-radius: 8px;
    margin-top: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    box-sizing: border-box;
}
.snap-dropdown .dropdown-menu:not(.hidden) {
    display: block;
}
.snap-dropdown .dropdown-list {
    max-height: 200px;
    overflow-y: auto;
}
.snap-dropdown .dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
}
.snap-dropdown .dropdown-item:hover {
    background: #F3F4F6;
}
.snap-dropdown .search-input {
    width: 100%;
    border: none;
    outline: none;
    font-size: 12px;
    color: #1F2937;
    padding: 0;  
    background: transparent;
}
.snap-dropdown .search-input::placeholder {
    color: #9CA3AF;
}

.database-container {
  width: 100%;
  margin-top: 32px;
  margin-bottom: 0;
  background: transparent;
  box-sizing: border-box;
  /* overflow: hidden; */
}
.database-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 24px;
  width: 100%;
  box-sizing: border-box;
}
.database-left {
  display: flex;
  align-items: center;
  flex: 1 1 auto;
  min-width: 0;
}
.database-stat {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 4px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  color: #606F95;
  font-weight: 700;
  min-width: 120px;
}
.database-label {
  font-size: 14px;
  font-weight: 700;
  color: #606F95;
  margin-bottom: 0;
  text-align: left;
}
.database-value-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
  width: 100%;
}
.database-value {
  font-size: 14px;
  font-weight: 700;
  color: #606F95;
  margin-left: 0;
  text-align: left;
}
.database-icon {
  width: 12px;
  height: 12px;
  margin-right: 0;
}
.database-updated-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #04AE2C;
  margin-top: 2px;
}
.database-updated-row img {
  width: 12px;
  height: 12px;
  margin-right: 0;
}
.database-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1px;
  height: 35px;
  background: #606F95;
  opacity: 0.1;
  margin: 0 16px;
}
[data-theme="dark"] .database-divider {
  background: #B4B9C5;
  opacity: 0.1;
}
.database-status .database-updated {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  color: #04AE2C;
  margin-left: 8px;
  gap: 4px;
}
.database-updated img {
  width: 12px;
  height: 12px;
  margin-right: 4px;
}
.database-update-btn {
  width: 100px !important;
  min-width: 100px !important;
  margin-left: 24px;
  background: #04AE2C !important;
  color: #fff !important;
  border-radius: 26px !important;
  font-size: 12px;
  font-weight: 700;
  font-family: 'Amazon Ember', sans-serif;
  height: 32px !important;
  box-shadow: none;
  border: none;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}
.database-update-btn:hover {
  background: rgba(4, 174, 44, 0.8) !important;
}
.database-marketplace-dropdown {
  width: auto;
  min-width: 180px;
  max-width: 220px;
  flex: 0 0 auto;
  margin-left: 16px;
}
.database-marketplace-dropdown .dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #FFFFFF;
  border: 1.5px solid #E9EBF2;
  border-radius: 8px;
  height: 40px;
  padding: 12px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #606F95;
  cursor: pointer;
  min-width: 0;
  width: auto;
  box-sizing: border-box;
}
.database-marketplace-dropdown .dropdown-header span {
  padding: 0;
  margin: 0;
  white-space: nowrap;
}
.database-marketplace-dropdown .dropdown-header-content {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 0 0 auto;
}
.database-marketplace-dropdown .dropdown-header img[alt="Dropdown"] {
  margin-right: 0;
  margin-left: 8px;
  flex-shrink: 0;
}
.database-marketplace-dropdown .dropdown-header img[alt="All Marketplaces Icon"] {
  margin-right: 0;
  flex-shrink: 0;
}

/* Ensure dropdown header adjusts to content */
.database-container .database-marketplace-dropdown .dropdown-header {
  min-width: 0 !important;
  width: auto !important;
}
@media (max-width: 1200px) {
  .database-row {
    flex-wrap: wrap;
    gap: 16px;
  }
  .database-update-btn {
    min-width: 100px;
    width: 100px;
    font-size: 12px;
    height: 32px;
  }
  .database-marketplace-dropdown {
    min-width: 140px;
    width: auto;
  }
  .database-marketplace-dropdown .dropdown-header {
    min-width: 0; /* Fix overflow: allow header to shrink with container */
  }
}
@media (max-width: 800px) {
  .database-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  .database-update-btn, .database-marketplace-dropdown {
    margin-left: 0;
  }
}

.database-label-row {
  display: flex;
  align-items: center;
  gap: 8px;
  text-align: left;
}

.listing-status-top,
.database-value-row,
.database-updated-row,
.database-label-row {
  display: flex;
  align-items: center;
}

.database-label-row .database-label {
  margin-top: 2px;
}

.database-value-row > .database-value,
.database-updated-row > .database-updated-text,
.database-label-row > .database-label,
.listing-status-top > .listing-status-number {
  margin-top: 2px;
}

.flex-row {
  display: flex;
  align-items: center;
}
.flex-row > img + span,
.flex-row > img + div,
.flex-row > svg + span,
.flex-row > svg + div {
  margin-top: 2px;
}

/* Update existing row classes to use .flex-row for DRYness */
.database-value-row,
.database-updated-row,
.database-label-row,
.listing-status-top {
  composes: flex-row;
}

.designs-stat, .products-stat {
  width: fit-content;
  min-width: 0;
}

.database-updated-text {
  font-size: 10px;
}

[data-theme="dark"] .database-marketplace-dropdown .dropdown-header,
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu {
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color) !important;
  border-radius: 8px;
  color: var(--text-primary);
}
[data-theme="dark"] .database-marketplace-dropdown .dropdown-header span,
[data-theme="dark"] .database-marketplace-dropdown .dropdown-item span {
  color: var(--text-primary) !important;
}

.designs-value, .products-value {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: 1.2;
  margin-top: 2px;
}
[data-theme="dark"] .designs-value,
[data-theme="dark"] .products-value {
  color: #B4B9C5;
}

.hidden {
  display: none !important;
}

[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item:hover,
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item.selected {
  background: #292E38 !important;
  color: var(--text-accent) !important;
}

.snap-dropdown .dropdown-item:first-child:hover {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
.snap-dropdown .dropdown-item:last-child:hover {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item:first-child:hover,
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item:first-child.selected {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item:last-child:hover,
[data-theme="dark"] .database-marketplace-dropdown .dropdown-menu .dropdown-item:last-child.selected {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

/* --- Ad Spend Section Styles (Figma-accurate, matches screenshot) --- */
.ad-spend {
  width: 100%;
  background: #fff;
  border: 1.5px solid #E9EBF2;
  border-radius: 14px;
  padding: 24px !important;
  margin-top: 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 18px;
  position: relative !important;
}
.ad-spend-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.ad-spend-header-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}
.ad-spend-header-center {
  display: flex;
  align-items: center;
}
.ad-spend-header-right {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 8px;
}
.ad-spend-ads-pill {
  background: #E9EBF2;
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 15px;
  border-radius: 6px;
  padding: 2px 14px 2px 14px;
  letter-spacing: 0.02em;
  display: inline-block;
}
.ad-spend-today-label {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 500;
  position: relative;
  top: 1px;
}
.ad-spend-header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}
.ad-spend-header-label {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
}
.ad-spend-header-value {
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
}
.ad-spend-nav-btn {
  background: none !important;
  border: none !important;
  width: auto !important;
  height: auto !important;
  box-shadow: none !important;
  padding: 0 !important;
  min-width: 0 !important;
  min-height: 0 !important;
  max-width: none !important;
  max-height: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ad-spend-nav-btn img {
  width: 16px !important;
  height: 16px !important;
}
.ad-spend-marketplaces-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 0;
}
.ad-spend-marketplace-col,
.ad-spend-marketplace-col-us,
.ad-spend-marketplace-col-uk,
.ad-spend-marketplace-col-de,
.ad-spend-marketplace-col-fr,
.ad-spend-marketplace-col-it,
.ad-spend-marketplace-col-es,
.ad-spend-marketplace-col-jp {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: auto;
}
.ad-spend-flag {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-bottom: 6px;
  box-shadow: 0 1px 2px rgba(96, 111, 149, 0.08);
}
.ad-spend-value.ad-spend-currency {
  font-size: 14px;
  font-weight: 500;
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
  margin-bottom: 2px;
  letter-spacing: 0.01em;
  text-align: center;
}
.ad-spend-orders {
  font-size: 10px;
  font-weight: 500;
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
}
.ad-spend-acos-pill, .ad-spend-acos-green, .ad-spend-acos-red, .ad-spend-ads-pill {
  background: none !important;
  border: none !important;
  color: inherit !important;
  padding: 0 !important;
  margin: 0 2px 0 2px !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  display: inline-flex !important;
  align-items: center !important;
}
.ad-spend-acos-green {
  background: #04AE2C;
  color: #fff;
}
.ad-spend-acos-red {
  background: #FF391F;
  color: #fff;
}
.ad-spend-acos-value {
  font-size: 8px;
  font-weight: 400;
  color: #606F95;
  font-family: 'Amazon Ember', sans-serif;
}
.ad-spend-marketplace-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
  width: 1px;
  height: 35px;
  background: #606F95;
  opacity: 0.1;
  margin: 0 8px;
}
/* Hide divider for last column */
.ad-spend-marketplace-col:last-of-type + .ad-spend-marketplace-divider,
.ad-spend-marketplace-col-jp + .ad-spend-marketplace-divider {
  display: none;
}
/* Responsive adjustments */
@media (max-width: 1200px) {
  .ad-spend-marketplaces-row {
    flex-wrap: wrap;
  }
  .ad-spend-marketplace-col,
  .ad-spend-marketplace-col-us,
  .ad-spend-marketplace-col-uk,
  .ad-spend-marketplace-col-de,
  .ad-spend-marketplace-col-fr,
  .ad-spend-marketplace-col-it,
  .ad-spend-marketplace-col-es,
  .ad-spend-marketplace-col-jp {
    min-width: 0;
    width: auto;
    padding: 0;
  }
}
@media (max-width: 900px) {
  .ad-spend-header-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  .ad-spend-header-center {
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 6px;
  }
  .ad-spend-marketplaces-row {
    flex-direction: column;
    gap: 12px;
  }
  .ad-spend-marketplace-divider {
    display: none;
  }
  .ad-spend-marketplace-col,
  .ad-spend-marketplace-col-us,
  .ad-spend-marketplace-col-uk,
  .ad-spend-marketplace-col-de,
  .ad-spend-marketplace-col-fr,
  .ad-spend-marketplace-col-it,
  .ad-spend-marketplace-col-es,
  .ad-spend-marketplace-col-jp {
    min-width: 0;
    width: auto;
    padding: 0;
  }
}
@media (max-width: 600px) {
  .ad-spend {
    padding: 10px 4px 8px 4px;
  }
  .ad-spend-header-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
  .ad-spend-header-center {
    flex-wrap: wrap;
    gap: 4px;
  }
  .ad-spend-marketplaces-row {
    flex-direction: column;
    gap: 8px;
  }
  .ad-spend-marketplace-col,
  .ad-spend-marketplace-col-us,
  .ad-spend-marketplace-col-uk,
  .ad-spend-marketplace-col-de,
  .ad-spend-marketplace-col-fr,
  .ad-spend-marketplace-col-it,
  .ad-spend-marketplace-col-es,
  .ad-spend-marketplace-col-jp {
    min-width: 0;
    width: 100%;
    padding: 0;
  }
}
[data-theme="dark"] .ad-spend {
  background: var(--bg-primary);
  border-color: var(--border-color);
}
[data-theme="dark"] .ad-spend-header-row,
[data-theme="dark"] .ad-spend-header-center,
[data-theme="dark"] .ad-spend-header-label,
[data-theme="dark"] .ad-spend-header-value,
[data-theme="dark"] .ad-spend-orders,
[data-theme="dark"] .ad-spend-acos-value {
  color: #B4B9C5;
}
[data-theme="dark"] .ad-spend-ads-pill {
  background: #292E38;
  color: #B4B9C5;
}
[data-theme="dark"] .ad-spend-marketplace-divider {
  background: #B4B9C5;
  opacity: 0.1;
}


[data-theme="dark"] .ad-spend-value {
  color: #fff;
}
[data-theme="dark"] .ad-spend-acos-green {
  background: #04AE2C;
  color: #fff;
}
[data-theme="dark"] .ad-spend-acos-red {
  background: #FF391F;
  color: #fff;
}
/* --- End Ad Spend Section Styles --- */

.ad-spend-acos-pill {
  width: 24px !important;
  height: 12px !important;
  min-width: 24px !important;
  min-height: 12px !important;
  max-width: 24px !important;
  max-height: 12px !important;
  object-fit: contain;
  display: inline-block !important;
}
.ad-spend-orders-row {
  display: flex;
  align-items: center;
  gap: 4px;
}

[data-theme="dark"] .ad-spend-today-label {
  color: #fff !important;
}

.ad-spend-header-center {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
}
.ad-spend-header-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1px;
  height: 17.5px;
  background: #606F95;
  opacity: 0.1;
  margin: 0 8px;
}
[data-theme="dark"] .ad-spend-header-divider {
  background: #B4B9C5;
  opacity: 0.1;
}

.ad-spend-header-metric-group {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  width: fit-content;
}



.ad-spend-nav-btn.ad-spend-prev:hover,
.ad-spend-nav-btn.ad-spend-next:hover {
  opacity: 0.5;
}

.designs-label, .products-label {
  font-size: 14px;
  font-weight: 500;
  color: #606F95;
  margin-bottom: 0;
  text-align: left;
}

/* Add pointer cursor for ad spend nav buttons on hover */
.ad-spend-nav-btn:hover {
  cursor: pointer;
}

/* Disabled state for ad spend navigation buttons */
.ad-spend-nav-btn[style*="pointer-events: none"],
.ad-spend-nav-btn[aria-disabled="true"] {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.ad-spend-nav-btn[style*="pointer-events: none"]:hover,
.ad-spend-nav-btn[aria-disabled="true"]:hover {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Ensure disabled buttons don't show hover effects */
.ad-spend-nav-btn[style*="pointer-events: none"] img,
.ad-spend-nav-btn[aria-disabled="true"] img {
  filter: grayscale(100%) !important;
  transition: filter 0.2s ease;
}

.ad-spend-header-right-group {
  display: flex;
  align-items: center;
  gap: 32px;
}

[data-theme="dark"] .database-label {
  color: #FFFFFF !important;
}

/* Prevent containers shrinking below 1024px */
.dashboard-component,
.account-status,
.listings-status,
.listings-status-overview,
.ad-spend,
.database-container,
.sales-cards-container,
.last-week-sales-card,
.today-vs-previous-years-card {
  min-width: 1024px !important;
}

/* Fix listing overview and ad-spend to maintain desktop layout at small widths */
@media (max-width: 1023px) {
  .listings-status-overview,
  .ad-spend,
  .last-week-sales-card {
    min-width: 1024px !important;
    width: auto !important;
  }
  /* Listing Overview Layout */
  .listings-status-row {
    flex-wrap: nowrap !important;
    flex-direction: row !important;
  }
  .listing-status-card {
    flex: 0 0 auto !important;
    width: auto !important;
    min-width: auto !important;
  }
  .listing-status-divider {
    display: flex !important;
  }
  /* Ad Spend Layout */
  .ad-spend-header-row {
    flex-direction: row !important;
  }
  .ad-spend-marketplaces-row {
    flex-wrap: nowrap !important;
  }
}

/* Enforce desktop layout for Listings Overview, Ad Spend, and Last Week Sales */
.listings-status-overview,
.ad-spend,
.last-week-sales-card {
  min-width: 1024px !important;
  width: auto !important;
  padding: 24px !important;
}
.listings-status-row,
.ad-spend-header-row,
.ad-spend-marketplaces-row {
  flex-wrap: nowrap !important;
  flex-direction: row !important;
}

/* Global fixed-width enforcement: any top-level container inside main content respects 1024px rule */
.main-content > * {
  min-width: 1024px !important;
}

/* Auto enforce min-width on container classes within main content */
.main-content [class$="-container"] {
  min-width: 1024px !important;
}

/* Prevent database container from wrapping its content on narrow viewports */
.database-container .database-row {
  flex-wrap: nowrap !important;
  flex-direction: row !important;
}
/* Keep marketplace dropdown auto width */
.database-container .database-marketplace-dropdown {
  min-width: 180px !important;
  width: auto !important;
  margin-left: 16px !important;
}

/* Sales Cards Container */
.sales-cards-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
  min-width: 1024px;
  align-items: flex-start; /* Allow cards to have different heights */
}

/* First row container for Today's and Yesterday's cards */
.sales-cards-row {
  width: 100%;
  display: flex;
  gap: 16px;
}

/* Today's and Yesterday's cards - side by side */
.sales-cards-container > .Sales-card-div {
  flex: 1;
  width: calc(50% - 8px);
}

/* Sales Card Styles (Figma accurate) */
.Sales-card-div {
  flex: 1;
  background: #fff;
  border: 1.5px solid #E9EBF2;
  border-radius: 14px;
  padding: 24px; /* Equal padding all around - scrollbar is overlay and doesn't affect layout */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 880px; /* Fixed height */
  min-height: 880px; /* Fixed minimum height */
  max-height: 880px; /* Fixed maximum height */
  overflow-y: auto; /* Enable vertical scrolling */
  overflow-x: hidden; /* Completely forbid horizontal scrolling */
  align-self: flex-start; /* Prevent stretching to match tallest card */
}

/* Modern scrollbar - overlay style to prevent width changes */
.Sales-card-div::-webkit-scrollbar {
  width: 6px;
  background: transparent;
  position: absolute;
  z-index: 999;
}

/* Hide horizontal scrollbar completely */
.Sales-card-div::-webkit-scrollbar:horizontal {
  display: none;
  height: 0px;
}

/* Modern vertical scrollbar - always reserve space but show handle on hover */
.Sales-card-div::-webkit-scrollbar:vertical {
  width: 6px;
}

/* Ensure horizontal scrollbar stays hidden */
.Sales-card-div:hover::-webkit-scrollbar:horizontal,
.Sales-card-div:focus-within::-webkit-scrollbar:horizontal {
  display: none;
  height: 0px;
}

/* Hide scrollbar track completely for modern look */
.Sales-card-div::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 0;
  margin: 0;
}

/* Modern scrollbar handle - light theme */
.Sales-card-div::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
  border: none;
  transition: background 0.2s ease;
}

.Sales-card-div:hover::-webkit-scrollbar-thumb,
.Sales-card-div:focus-within::-webkit-scrollbar-thumb {
  background: rgba(96, 111, 149, 0.4);
}

.Sales-card-div::-webkit-scrollbar-thumb:hover {
  background: rgba(96, 111, 149, 0.6);
  border-radius: 3px;
}

/* Firefox modern scrollbar - true overlay behavior */
.Sales-card-div {
  scrollbar-width: none; /* Hide scrollbar completely by default */
  scrollbar-color: transparent transparent; /* Hide by default */
  overflow-x: hidden !important; /* Completely forbid horizontal scrollbar */
  overflow-y: auto; /* Allow vertical scrolling */
}

/* Firefox: Show scrollbar on hover but keep it overlay */
.Sales-card-div:hover {
  scrollbar-width: thin; /* Show thin scrollbar on hover */
  scrollbar-color: rgba(96, 111, 149, 0.4) transparent; /* Show on hover */
}

/* Firefox: Prevent layout shift by using overlay positioning */
@supports (scrollbar-width: thin) {
  .Sales-card-div {
    /* Create a wrapper effect to contain the scrollbar overlay */
    position: relative;
    overflow-y: scroll; /* Force scrollbar space reservation */
    scrollbar-width: thin; /* Always reserve space */
    scrollbar-color: transparent transparent; /* But keep it invisible */
  }
  
  .Sales-card-div:hover {
    scrollbar-color: rgba(96, 111, 149, 0.4) transparent; /* Show on hover */
  }
}

[data-theme="dark"] .Sales-card-div {
  background: var(--bg-primary);
  border-color: var(--border-color);
}

/* Dark theme modern scrollbar */
[data-theme="dark"] .Sales-card-div::-webkit-scrollbar-thumb {
  background: transparent;
}

[data-theme="dark"] .Sales-card-div:hover::-webkit-scrollbar-thumb,
[data-theme="dark"] .Sales-card-div:focus-within::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] .Sales-card-div::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Firefox dark theme */
[data-theme="dark"] .Sales-card-div {
  scrollbar-color: transparent transparent; /* Hide by default */
}

[data-theme="dark"] .Sales-card-div:hover {
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent; /* Show on hover */
}

/* Dark theme Firefox overlay support */
@supports (scrollbar-width: thin) {
  [data-theme="dark"] .Sales-card-div {
    scrollbar-color: transparent transparent; /* Keep invisible by default */
  }
  
  [data-theme="dark"] .Sales-card-div:hover {
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent; /* Show on hover */
  }
}

/* Modern scrollbar for main page - both vertical and horizontal allowed */
body::-webkit-scrollbar {
  width: 0px;
  height: 0px;
  background: transparent;
  transition: width 0.2s ease, height 0.2s ease;
}

/* Show modern scrollbar on hover/scroll */
body:hover::-webkit-scrollbar:vertical,
body:focus-within::-webkit-scrollbar:vertical {
  width: 6px;
}

body:hover::-webkit-scrollbar:horizontal,
body:focus-within::-webkit-scrollbar:horizontal {
  height: 6px;
}

/* Modern scrollbar track - invisible */
body::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

/* Modern scrollbar thumb */
body::-webkit-scrollbar-thumb {
  background: rgba(96, 111, 149, 0.4);
  border-radius: 3px;
  transition: background 0.2s ease;
}

body:hover::-webkit-scrollbar-thumb,
body:focus-within::-webkit-scrollbar-thumb {
  background: rgba(96, 111, 149, 0.5);
}

body::-webkit-scrollbar-thumb:hover {
  background: rgba(96, 111, 149, 0.7);
}

/* Firefox modern scrollbar for main page */
body {
  scrollbar-width: none; /* Hide by default */
  scrollbar-color: transparent transparent;
}

body:hover {
  scrollbar-width: thin;
  scrollbar-color: rgba(96, 111, 149, 0.4) transparent;
}

/* Dark mode scrollbar for main page */
[data-theme="dark"] body::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] body::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

[data-theme="dark"] body:hover {
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}
.Sales-title-date-div {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  margin-bottom: 16px;
}
.sales-card-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
}
.sales-card-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #606F95;
}
[data-theme="dark"] .sales-card-title {
  color: #B4B9C5;
}
.sales-card-date {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #606F95;
  /* No extra margin */
}
[data-theme="dark"] .sales-card-date {
  color: #B4B9C5;
}

/* Sales Analytics Div (Figma accurate) */
.sales-analytics-div {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 16px;
  gap: 0;
}
.sales-count-div {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 32px;
  margin-right: 24px; /* Ensure minimum gap to analytics-div */
}
.sales-count {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 800;
  font-size: 48px;
  color: #470CED;
  line-height: 1.2;
  text-align: right;
}
.analytics-div {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 473px;
  gap: 0;
  margin-top: 10px;
}
.metric-col {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  /* Remove right padding */
  padding-right: 0;
  min-width: 0;
}
.metric-top {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
}
.metric-value {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #606F95;
  line-height: 1.2;
  margin-top: 2px;
}
.metric-value.royalties {
  color: #04AE2C;
}
.metric-value.royalties.negative {
  color: #FF391F !important;
}
.metric-value.returned {
  color: #FF391F;
  white-space: nowrap;
}
.metric-value.zero {
  color: #606F95 !important;
}
.metric-value.cancelled.has-value {
  color: #FDC300 !important;
}
.metric-value.ads.has-value,
.metric-value.new.has-value {
  color: #04AE2C !important;
}
.metric-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  color: #606F95;
  line-height: 1.2;
}
.metric-icon {
  width: 12px;
  height: 12px;
  display: inline-block;
}
.metric-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1px;
  height: 35px;
  background: #606F95;
  opacity: 0.1;
  margin: 0 8px;
}
@media (max-width: 900px) {
  .analytics-div {
    width: 100%;
    gap: 0;
  }
  .sales-analytics-div {
    flex-direction: column;
    align-items: stretch;
  }
  .sales-count-div {
    padding-right: 0;
    margin-bottom: 16px;
    margin-right: 0;
    justify-content: flex-start;
  }
}
[data-theme="dark"] .metric-value.royalties {
  color: #04AE2C;
}
[data-theme="dark"] .metric-value.royalties.negative {
  color: #FF391F !important;
}
[data-theme="dark"] .metric-value.returned {
  color: #FF391F;
}
[data-theme="dark"] .metric-value,
[data-theme="dark"] .metric-label {
  color: #B4B9C5;
}
[data-theme="dark"] .metric-value.zero {
  color: #B4B9C5 !important;
}
[data-theme="dark"] .metric-value.cancelled.has-value {
  color: #FDC300 !important;
}
[data-theme="dark"] .metric-value.ads.has-value,
[data-theme="dark"] .metric-value.new.has-value {
  color: #04AE2C !important;
}
[data-theme="dark"] .metric-divider {
  background: #B4B9C5;
  opacity: 0.1;
}

/* Sales Section Divider */
.sales-section-divider {
  width: 100%;
  height: 1.5px;
  background: rgba(232, 235, 244, 0.5);
  border: none;
  margin: 24px 0 24px 0;
  flex-shrink: 0;
}
[data-theme="dark"] .sales-section-divider {
  background: #B4B9C5;
  opacity: 0.1;
}

/* Marketplaces Div (Figma accurate) */
.marketplaces-div {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  min-width: 1024px;
}
.marketplaces-sales-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 0;
  width: 100%;
  min-width: 1024px;
}
.marketplace-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  /* Remove min-width and flex so width is determined by children */
  min-width: unset;
  flex: unset;
  cursor: pointer;
  transition: opacity 0.3s ease, transform 0.2s ease;
  border-radius: 8px;
  padding: 8px;
  position: relative;
}

.marketplace-col:hover {
  transform: translateY(-2px);
  background: rgba(96, 111, 149, 0.05);
}

.marketplace-col.all-marketplaces.active:hover,
.single-marketplace-active .marketplace-col.active:hover {
  transform: none;
  background: transparent;
}

[data-theme="dark"] .marketplace-col:hover {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .marketplace-col.all-marketplaces.active:hover,
[data-theme="dark"] .single-marketplace-active .marketplace-col.active:hover {
  background: transparent;
}

.marketplace-col.active {
  opacity: 1;
}

.marketplace-col.inactive {
  opacity: 0.5;
}

.marketplace-col.inactive .marketplace-icon {
  filter: grayscale(100%);
}

/* Remove opacity and grayscale from inactive marketplace columns on hover */
.marketplace-col.inactive:hover {
  opacity: 1;
}

.marketplace-col.inactive:hover .marketplace-icon {
  filter: none;
}

/* Custom tooltips for marketplace columns are handled by JavaScript */
/* This ensures the default tooltips don't interfere */
.marketplace-col[data-tooltip]:hover:before,
.marketplace-col[data-tooltip]:hover:after {
  display: none !important;
}

/* Force hide tooltips when marketplace column is clicked/focused */
.marketplace-col[data-tooltip]:focus:before,
.marketplace-col[data-tooltip]:focus:after {
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Also hide on active state to prevent tooltip persistence */
.marketplace-col[data-tooltip]:active:before,
.marketplace-col[data-tooltip]:active:after {
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Ensure smooth transitions for all marketplace elements */
.marketplace-col * {
  transition: var(--theme-transition);
}

/* Focus states for accessibility */
.marketplace-col:focus {
  outline: none;
}
.marketplace-icon {
  width: 28px;
  height: 28px;
  margin-bottom: 2px;
}
.marketplace-total-sales-count {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #606F95;
}
.marketplace-total-sales-count.zero {
  color: #606F95;
}
.marketplace-total-earned-royalties {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 11px;
  color: #04AE2C;
}
.marketplace-total-earned-royalties.zero {
  color: #606F95;
}
.marketplace-total-earned-royalties.negative {
  color: #FD5900;
}
.marketplace-total-returned-units {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #FD5900;
}
.marketplace-total-returned-units.zero {
  color: #606F95;
}
@media (max-width: 900px) {
  .marketplaces-sales-row {
    flex-wrap: nowrap !important;
    gap: 0;
  }
  .marketplace-col {
    min-width: 0;
    flex: unset;
  }
}
[data-theme="dark"] .marketplace-total-sales-count {
  color: #B4B9C5;
}
[data-theme="dark"] .marketplace-total-sales-count.zero {
  color: #B4B9C5;
}
[data-theme="dark"] .marketplace-total-earned-royalties {
  color: #04AE2C;
  font-size: 11px;
}
[data-theme="dark"] .marketplace-total-earned-royalties.zero {
  color: #B4B9C5;
}
[data-theme="dark"] .marketplace-total-earned-royalties.negative {
  color: #FD5900;
}
[data-theme="dark"] .marketplace-total-returned-units {
  color: #FD5900;
}
[data-theme="dark"] .marketplace-total-returned-units.zero {
  color: #B4B9C5;
}

.search-tabs-div {
  width: 100%;
  flex-direction: column;
  gap: 10px;
  margin-top: 24px;
  min-width: 1024px;
}
.search-div {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
}
.search-input-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  background: #fff;
  border: 1.5px solid #E9EBF2;
  border-radius: 8px;
  padding: 10px;
  width: 100%;
  min-width: 0;
  box-sizing: border-box;
  gap: 8px;
}
[data-theme="dark"] .search-input-wrapper {
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color);
}
.search-sales-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
}
.search-input {
  border: none;
  outline: none;
  background: transparent;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  color: #606F95;
  flex: 1 1 auto;
  min-width: 0;
}

.search-input::placeholder {
  color: #606F95;
  opacity: 0.5;
}

[data-theme="dark"] .search-input {
  color: #B4B9C5;
}

[data-theme="dark"] .search-input::placeholder {
  color: #FFFFFF;
  opacity: 0.7;
}
.close-search-icon {
  width: 19px;
  height: 19px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.close-search-icon:hover {
  opacity: 0.7 !important;
}

.search-input:focus + .close-search-icon {
  opacity: 1;
}
.sales-filter-div {
  width: 100%;
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background: rgba(232, 235, 244, 0.5);
  border-radius: 6px;
  padding: 3px;
  gap: 3px;
  box-sizing: border-box;
}
[data-theme="dark"] .sales-filter-div {
  background: #292E38;
}
.sales-filter-tab {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 0 8px;
  border-radius: 4px;
  cursor: pointer;
  background: transparent;
  height: 34px;
  flex: 1 1 0;
  justify-content: center;
  min-width: 0;
  position: relative;
}
.sales-filter-tab.active {
  background: #fff;
  box-shadow: 0 2px 8px rgba(96, 111, 149, 0.04);
}

/* Hover state for inactive sales filter tabs only */
.sales-filter-tab:not(.active):hover {
  background: rgba(96, 111, 149, 0.04);
  transition: background 0.2s ease;
}

[data-theme="dark"] .sales-filter-tab.active {
  background: var(--bg-primary);
  box-shadow: none;
}

/* Dark theme hover state for inactive sales filter tabs only */
[data-theme="dark"] .sales-filter-tab:not(.active):hover {
  background: rgba(255, 255, 255, 0.05);
}
.tab-icon {
  width: 16px;
  height: 16px;
}
.tab-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 11px;
  color: #606F95;
  display: block; /* Show text labels */
  margin-top: 2px;
}
[data-theme="dark"] .tab-label {
  color: #B4B9C5;
}
.tab-sort-icon {
  width: 14px;
  height: 14px;
  margin-left: 2px;
}
@media (max-width: 900px) {
  .search-tabs-div {
    min-width: 0;
    width: 100%;
  }
  .search-input-wrapper {
    width: 100%;
    min-width: 0;
  }
  .sales-filter-div {
    flex-wrap: wrap;
    width: 100%;
  }
}

.tab-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  justify-content: center;
  flex: 1 1 auto;
  min-width: 0;
  position: absolute;
  left: 0;
  right: 0;
  height: 100%;
}
.tab-sort-icon {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 1;
}
.tab-sort-icon.active {
  opacity: 1;
  pointer-events: auto;
}

/* Fix tooltip hover detection for all tabs with tooltips */
.sales-filter-tab .tab-main,
.sales-filter-tab .tab-label,
.sales-filter-tab .tab-sort-icon {
  pointer-events: none;
}

/* Ensure the parent tab remains clickable */
.sales-filter-tab {
  pointer-events: auto;
}

/* Fix tooltip hover detection for privacy mode toggle tabs */
.privacy-mode-toggle .off-tab .off-div,
.privacy-mode-toggle .off-tab .off-div span,
.privacy-mode-toggle .off-tab .off-div img,
.privacy-mode-toggle .on-tab .on-div,
.privacy-mode-toggle .on-tab .on-div span,
.privacy-mode-toggle .on-tab .on-div img {
  pointer-events: none;
}

/* Ensure the parent privacy toggle tabs remain clickable */
.privacy-mode-toggle .off-tab,
.privacy-mode-toggle .on-tab {
  pointer-events: auto;
}

/* Fix tooltip hover detection for listing status cards */
.listing-status-card .listing-status-top,
.listing-status-card .listing-status-icon,
.listing-status-card .listing-status-icon img,
.listing-status-card .listing-status-number,
.listing-status-card .listing-status-label {
  pointer-events: none;
}

/* Ensure the parent listing status cards remain clickable */
.listing-status-card {
  pointer-events: auto;
}

/* Global fix for all tabs with tooltips - prevent child elements from blocking hover */
/* Target sales filter tabs and similar tab structures */
[data-tooltip].sales-filter-tab *,
[data-tooltip].time-tab *,
[data-tooltip].units-tab *,
[data-tooltip].royalties-tab *,
[data-tooltip].new-tab *,
[data-tooltip].ad-spend-tab *,
[data-tooltip][class$="-tab"]:not([class*="off-tab"]):not([class*="on-tab"]) * {
  pointer-events: none;
}

/* Ensure parent tabs with tooltips remain clickable */
[data-tooltip].sales-filter-tab,
[data-tooltip].time-tab,
[data-tooltip].units-tab,
[data-tooltip].royalties-tab,
[data-tooltip].new-tab,
[data-tooltip].ad-spend-tab,
[data-tooltip][class$="-tab"]:not([class*="off-tab"]):not([class*="on-tab"]) {
  pointer-events: auto;
}

/* Ensure all direct children of main-content and Sales-card-div never overflow parent */
.main-content > *,
.Sales-card-div > * {
  width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box;
}

/* Prevent any child elements from causing horizontal overflow in sales cards */
.Sales-card-div * {
  max-width: 100%;
  box-sizing: border-box;
}

/* Remove min-width from children of Sales-card-div and main-content */
.marketplaces-div,
.search-tabs-div,
.sales-analytics-div {
  min-width: 0 !important;
  width: 100% !important;
}

/* Ensure marketplaces-sales-row always fits parent width */
.marketplaces-sales-row {
  width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box;
}

/* --- Listing Analytics Section Styles --- */
.listing-analytics-div {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 20px;
  width: 100%;
  min-width: 0;
  margin-top: 24px;
  padding: 0;
  box-sizing: border-box;
  flex-wrap: nowrap;
  /* Smooth transitions for reordering */
  transition: opacity 0.15s ease-in-out, transform 0.15s ease-in-out;
}
.listing-left-div {
  width: 75px;
  height: 100px;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  background: none;
  border-radius: 8px;
}
.listing-product-img {
  width: 75px;
  height: 100px;
  border-radius: 8px;
  background: #000000; /* Default black background - will be overridden by JavaScript */
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  object-fit: cover;
  box-sizing: border-box; /* Ensure border doesn't affect dimensions */
}
.listing-right-div {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 160px;
  max-width: none;
  flex: 0 1 auto;
  gap: 12px;
}

.listing-middle-div {
  display: flex;
  flex-direction: column;
  flex: 1 1 0;
  min-width: 0;
  gap: 10px;
}
.listing-title-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 0;
  min-width: 0;
  position: relative;
}
.listing-badge-ic {
  width: 64px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 auto;
  align-self: center;
  margin: 0;
}
.listing-title-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  min-width: 0;
  flex: 1 1 0;
  flex-wrap: wrap;
}
.listing-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 13px;
  color: #606F95;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  max-width: 100%;
  flex: 1 1 0;
}
.listing-badges-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}
.listing-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #F7F8FA;
  border-radius: 6px;
  padding: 4px 10px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #606F95;
  min-height: 24px;
  position: relative;
}

/* Fix tooltip hover interruption by creating invisible overlay covering entire badge area */
.listing-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

/* Ensure badge content is above the overlay but still allows hover events */
.listing-badge > * {
  position: relative;
  z-index: 2;
  pointer-events: none;
}
.listing-badge img {
  width: 12px !important;
  height: 12px !important;
  min-width: 12px !important;
  min-height: 12px !important;
  max-width: 12px !important;
  max-height: 12px !important;
  margin-right: 0 !important;
  margin-left: 0 !important;
  vertical-align: middle !important;
}
.listing-badge.fit-types img {
  margin-right: 2px;
  margin-left: 2px;
}
.listing-badge.fit-types {
  gap: 2px;
  background: #F7F8FA;
  border-radius: 6px;
  padding: 4px 10px;
}
.listing-product-type-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 700;
  color: #606F95;
}
.listing-product-type-ic {
  width: 16px;
  height: 16px;
}
.listing-product-type {
  margin-top: 2px;
  font-size: 12px;
  font-weight: 500;
}
.listing-product-price {
  color: #04AE2C;
  font-weight: 700;
  margin-left: 8px;
  margin-top: 2px;
}

.listing-product-price-only {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.listing-product-price-only .listing-product-price {
  margin-left: 0;
  font-size: 16px;
  font-weight: 700;
}
.listing-ad-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #606F95;
}

/* Ad spend status colors */
.listing-ad-row.ad-no-sales .listing-ad-label {
  color: #606F95 !important;
  font-weight: 600 !important;
}

.listing-ad-row.ad-low .listing-ad-label {
  color: #04AE2C !important;
  font-weight: 600 !important;
}

.listing-ad-row.ad-medium .listing-ad-label {
  color: #FDC300 !important;
  font-weight: 600 !important;
}

.listing-ad-row.ad-high .listing-ad-label {
  color: #FF391F !important;
  font-weight: 600 !important;
}

.listing-ad-row.ad-default .listing-ad-label {
  color: #606F95 !important;
  font-weight: 600 !important;
}
.listing-ad-ic {
  width: 16px;
  height: 16px;
}
.listing-edit-analyse-row {
  display: flex;
  align-items: center;
  gap: 8px;
}
.listing-analyse-ic, .listing-edit-ic {
  position: relative;
  width: 32px;
  height: 32px;
  cursor: pointer;
  border-radius: 50%;
  background: #E9EBF2;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
}

.listing-analyse-ic img {
  width: 16px;
  height: 16px;
  position: relative;
  z-index: 1;
}

.listing-edit-ic img {
  width: 14px;
  height: 14px;
  position: relative;
  z-index: 1;
}

.listing-analyse-ic:hover {
  background: #04AE2C;
}

.listing-edit-ic:hover {
  background: #470CED;
}

/* Change icon source on hover using background approach */
.listing-analyse-ic:hover img {
  opacity: 0;
}

.listing-edit-ic:hover img {
  opacity: 0;
}

.listing-analyse-ic:hover::before {
  content: '';
  width: 16px;
  height: 16px;
  background: url('./assets/listing-analyse-hover-ic.svg') center/contain no-repeat;
  position: absolute;
  z-index: 2;
}

.listing-edit-ic:hover::before {
  content: '';
  width: 14px;
  height: 14px;
  background: url('./assets/edit-hover-ic.svg') center/contain no-repeat;
  position: absolute;
  z-index: 2;
}







/* Dark theme support */
[data-theme="dark"] .listing-analyse-ic,
[data-theme="dark"] .listing-edit-ic {
  background: var(--border-color);
}

[data-theme="dark"] .listing-analyse-ic:hover {
  background: #04AE2C;
}

[data-theme="dark"] .listing-edit-ic:hover {
  background: #470CED;
}
/* Responsive adjustments */
@media (max-width: 1200px) {
  .listing-analytics-div {
    display: grid;
    grid-template-columns: 75px 1fr;
    grid-template-rows: auto auto;
    grid-template-areas: 
      "image actions"
      "content content";
    gap: 16px 16px;
    padding: 0;
    align-items: start;
  }
  
  .listing-left-div {
    grid-area: image;
    align-self: start;
  }
  
  .listing-right-div {
    grid-area: actions;
    align-items: flex-end;
    min-width: 0;
    max-width: none;
    justify-self: end;
  }
  
  .listing-middle-div {
    grid-area: content;
    min-width: 0;
    width: 100%;
  }
}
@media (max-width: 800px) {
  .listing-analytics-div {
    display: grid;
    grid-template-columns: 75px 1fr;
    grid-template-rows: auto auto;
    grid-template-areas: 
      "image actions"
      "content content";
    gap: 12px 12px;
    padding: 0;
    align-items: start;
  }
  
  .listing-left-div {
    grid-area: image;
    align-self: start;
  }
  
  .listing-right-div {
    grid-area: actions;
    align-items: flex-end;
    min-width: 0;
    max-width: none;
    justify-self: end;
  }
  
  .listing-middle-div {
    grid-area: content;
    min-width: 0;
    width: 100%;
  }
  
  .listing-title {
    font-size: 14px;
  }
  .listing-badge {
    font-size: 11px;
    padding: 3px 6px;
  }
}
[data-theme="dark"] .listing-analytics-div {
  background: var(--bg-primary);
  border-color: var(--border-color);
}
[data-theme="dark"] .listing-badge,
[data-theme="dark"] .listing-product-type-row,
[data-theme="dark"] .listing-ad-row {
  color: #B4B9C5;
}

[data-theme="dark"] .listing-title {
  color: #FFFFFF;
}
[data-theme="dark"] .listing-badge {
  background: #292E38;
}
[data-theme="dark"] .listing-product-price {
  color: #04AE2C;
}

/* Sidebar: disable transitions on all children for instant theme toggle (preserve width on sidebar itself) */
.sidebar * {
  transition: none !important;
}

/* Listing-left-div states: loading, loaded, private */
.listing-left-div .preloader {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-width: 32px;
  min-height: 40px;
  background: #E9EBF2 url('./assets/snap-loader-ic.svg') center no-repeat;
  background-size: 32px 40px;
  position: relative;
  overflow: hidden;
}

/* Skeleton loader shimmer effect */
.listing-left-div .preloader::before {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 150%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 20%,
    rgba(255, 255, 255, 0.3) 40%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.3) 60%,
    rgba(255, 255, 255, 0.1) 80%,
    transparent 100%
  );
  animation: skeleton-shimmer 1.5s ease-in-out infinite;
}

@keyframes skeleton-shimmer {
  0% {
    left: -150%;
  }
  100% {
    left: 150%;
  }
}
.listing-left-div .listing-product-img,
.listing-left-div .privacy-icon,
.listing-left-div .privacy-text {
  display: none;
}

.listing-left-div.state-private .preloader {
  display: none;
}

.listing-left-div.state-loading .preloader {
  display: flex;
  width: 100%;
  height: 100%;
  background: #E9EBF2 url('./assets/snap-loader-ic.svg') center no-repeat;
  background-size: 32px 40px;
  position: relative;
  overflow: hidden;
}

.listing-left-div.state-loaded .listing-product-img {
  display: block;
}

.listing-left-div.state-private {
  background: #470CED;
}

.listing-left-div.state-private .privacy-icon {
  display: block;
  width: 24px;
  height: 24px;
  position: absolute;
  top: 12px;
  left: 12px;
}

.listing-left-div.state-private .privacy-text {
  display: block;
  position: absolute;
  bottom: 12px;
  left: 12px;
  right: 12px;
  color: #FFFFFF;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.2;
  text-align: left;
}

/* Privacy mode: hide specific badges but keep title visible */
.listing-left-div.state-private ~ .listing-middle-div .BSR-badge,
.listing-left-div.state-private ~ .listing-middle-div .Asin-badge,
.listing-left-div.state-private ~ .listing-middle-div .hot-seller-badge,
.listing-left-div.state-private ~ .listing-middle-div .instant-seller-badge,
.listing-left-div.state-private ~ .listing-middle-div .blazing-seller-badge,
.listing-left-div.state-private ~ .listing-middle-div .top-seller-badge,
.listing-left-div.state-private ~ .listing-middle-div .fresh-seller-badge,
.listing-left-div.state-private ~ .listing-middle-div .sale-badge {
  display: none;
}

.listing-left-div.state-private ~ .listing-middle-div .listing-title {
  color: #606F95;
  font-style: normal;
  font-size: 13px;
  font-size: 0; /* Hide original text */
}

.listing-left-div.state-private ~ .listing-middle-div .listing-title::after {
  content: 'Privacy Mode is enabled.';
  font-size: 13px;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
}

/* Dark theme: make privacy mode listing title white */
[data-theme="dark"] .listing-left-div.state-private ~ .listing-middle-div .listing-title {
  color: #FFFFFF;
}

/* Dev-mode: force loading spinner when no loaded/private state */
body.dev-mode .listing-left-div:not(.state-loaded):not(.state-private) .preloader {
  display: flex !important;
}

/* Dark theme for preloader */
[data-theme="dark"] .listing-left-div .preloader,
[data-theme="dark"] .listing-left-div.state-loading .preloader {
  background: var(--border-color) url('./assets/snap-loader-ic.svg') center no-repeat;
  background-size: 32px 40px;
}

/* Dark theme shimmer effect */
[data-theme="dark"] .listing-left-div .preloader::before {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.03) 20%,
    rgba(255, 255, 255, 0.08) 40%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.08) 60%,
    rgba(255, 255, 255, 0.03) 80%,
    transparent 100%
  );
}

/* Snap Image Studio specific override */
.snap-image-studio-component .tab-container {
  display: inline-flex !important;
  align-self: flex-start !important;
  min-width: 0 !important; /* Override global min-width rule */
  width: auto !important; /* Ensure auto width to fit content */
}

/* Notification override: shrink to fit content */
.main-content > .notification {
  display: inline-block !important;
  width: auto !important;
  box-sizing: border-box !important;
}

.fit-type {
  display: inline-flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 2px !important;
  vertical-align: middle !important;
  margin-top: 0 !important;
}

.fit-type img {
  width: 12px !important;
  height: 12px !important;
  min-width: 12px !important;
  min-height: 12px !important;
  max-width: 12px !important;
  max-height: 12px !important;
  margin: 0 !important;
  vertical-align: middle !important;
  margin-top: 0 !important;
}

.type-number {
  font-size: 12px !important;
  font-weight: 500 !important;
  margin-top: 2px !important;
  line-height: 1 !important;
}

.fit-type > *:not(img):not(.type-number) {
  margin-top: 0 !important;
}

.royalties-badge {
  /* Figma spec: green gradient bg, green border, green text, 6px radius */
  background: linear-gradient(90deg, rgba(4,174,44,0) 0%, rgba(4,174,44,0.10) 100%) !important;
  border: 1.3px solid rgba(4,174,44,0.2) !important;
  color: #04AE2C !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.royalties-badge img {
  /* Ensure icon uses Figma green */
  filter: none !important;
  color: #04AE2C !important;
  fill: #04AE2C !important;
}
[data-theme="dark"] .royalties-badge {
  /* Keep same green, but tweak bg for dark if needed */
  background: linear-gradient(90deg, rgba(4,174,44,0) 0%, rgba(4,174,44,0.18) 100%) !important;
  border: 1.3px solid rgba(4,174,44,0.3) !important;
  color: #04AE2C !important;
}
[data-theme="dark"] .royalties-badge img {
  color: #04AE2C !important;
  fill: #04AE2C !important;
  filter: none !important;
}

.listing-badge, .royalties-badge {
  height: 24px !important;
  min-height: 24px !important;
  max-height: 24px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 8px !important;
  padding-right: 8px !important;
  line-height: 1 !important;
  align-items: center !important;
  position: relative;
}

/* Apply same hover fix to royalties badges */
.royalties-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.royalties-badge > * {
  position: relative;
  z-index: 2;
  pointer-events: none;
}

.listing-badge img, .royalties-badge img {
  width: 12px !important;
  height: 12px !important;
  min-width: 12px !important;
  min-height: 12px !important;
  max-width: 12px !important;
  max-height: 12px !important;
  margin-right: 0 !important;
  margin-left: 0 !important;
  vertical-align: middle !important;
}

.listing-badge span, .royalties-badge span {
  margin-top: 2px !important;
  display: inline-block;
}

/* Override global badge span margin for fit-types badge */
.listing-badge.fit-types-badge .fit-type {
  margin-top: 0 !important;
}
.listing-badge.fit-types-badge .fit-type img {
  margin-top: 0 !important;
}

/* --- BADGE GRADIENTS & STROKES (Figma accurate) --- */

/* Lost Royalties Badge */
.lost-royalties-badge {
  background: linear-gradient(90deg, rgba(255,57,31,0) 0%, rgba(255,57,31,0.10) 100%) !important;
  border: 1.3px solid rgba(255,57,31,0.2) !important;
  color: #FF391F !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.lost-royalties-badge img {
  color: #FF391F !important;
  fill: #FF391F !important;
  filter: none !important;
}

/* Order Units Badge */
.order-units-badge {
  background: linear-gradient(90deg, rgba(4,174,44,0) 0%, rgba(4,174,44,0.10) 100%) !important;
  border: 1.3px solid rgba(4,174,44,0.2) !important;
  color: #04AE2C !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.order-units-badge img {
  color: #04AE2C !important;
  fill: #04AE2C !important;
  filter: none !important;
}

/* Returned Units Badge */
.returned-units-badge {
  background: linear-gradient(90deg, rgba(255,57,31,0) 0%, rgba(255,57,31,0.10) 100%) !important;
  border: 1.3px solid rgba(255,57,31,0.2) !important;
  color: #FF391F !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.returned-units-badge img {
  color: #FF391F !important;
  fill: #FF391F !important;
  filter: none !important;
}

/* Canceled Units Badge */
.canceled-units-badge {
  background: linear-gradient(90deg, rgba(253,195,0,0) 0%, rgba(253,195,0,0.10) 100%) !important;
  border: 1.3px solid rgba(253,195,0,0.2) !important;
  color: #FDC300 !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.canceled-units-badge img {
  color: #FDC300 !important;
  fill: #FDC300 !important;
  filter: none !important;
}

/* Ordered Colors Badge - Special styling with color circles */
.ordered-colors-badge {
  background: linear-gradient(90deg, rgba(96,111,149,0) 0%, rgba(96,111,149,0.10) 100%) !important;
  border: 1.3px solid rgba(96,111,149,0.2) !important;
  color: #606F95 !important;
  border-radius: 6px !important;
  font-weight: 500;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}
.ordered-colors-badge img {
  color: #606F95 !important;
  fill: #606F95 !important;
  filter: none !important;
}
.color-circles {
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  margin-top: 0 !important;
}
.color-item {
  display: flex !important;
  align-items: center !important;
  gap: 2px !important;
  margin-top: 0 !important;
}
.color-circle {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  display: inline-block !important;
  border: 0.5px solid rgba(96,111,149,0.2) !important;
  flex-shrink: 0 !important;
  margin-top: 0 !important;
}
.color-number {
  font-size: 12px !important;
  font-weight: 500 !important;
  color: #606F95 !important;
  margin-top: 2px !important;
  line-height: 1 !important;
}

/* Override global badge span margin for ordered colors badge (except color-number) */
.ordered-colors-badge .color-circles,
.ordered-colors-badge .color-item,
.ordered-colors-badge .color-circle {
  margin-top: 0 !important;
}

/* Dark theme support for ordered colors badge */
[data-theme="dark"] .ordered-colors-badge {
  background: linear-gradient(90deg, rgba(96,111,149,0) 0%, rgba(96,111,149,0.18) 100%) !important;
  border: 1.3px solid rgba(96,111,149,0.3) !important;
  color: #B4B9C5 !important;
}
[data-theme="dark"] .ordered-colors-badge img {
  color: #B4B9C5 !important;
  fill: #B4B9C5 !important;
}
[data-theme="dark"] .color-circle {
  border: 0.5px solid rgba(180,185,197,0.3) !important;
}
[data-theme="dark"] .color-number {
  color: #B4B9C5 !important;
}

/* Ordered Colors Custom Tooltip */
.ordered-colors-tooltip {
  position: absolute;
  left: 50%;
  bottom: calc(100% + 8px);
  transform: translateX(-50%);
  background: var(--tooltip-bg, #000000) !important;
  color: var(--tooltip-text, #FFFFFF) !important;
  border-radius: 10px;
  padding: 12px;
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: var(--tooltip-font-size, 12px);
  font-weight: 500;
  z-index: 10001;
  min-width: 120px;
  text-align: left;
  line-height: 1.5;
  pointer-events: none;
  display: none;
  white-space: normal;
}
.ordered-colors-tooltip-title {
  font-weight: 700;
  font-size: var(--tooltip-font-size, 12px);
  margin-bottom: 8px;
  color: var(--tooltip-text, #FFFFFF);
}
.ordered-colors-tooltip-row {
  font-size: var(--tooltip-font-size, 12px);
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  color: var(--tooltip-text, #FFFFFF);
}
.ordered-colors-tooltip-row:last-child {
  margin-bottom: 0;
}
.ordered-colors-tooltip-arrow {
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: var(--tooltip-arrow-size, 5px) solid transparent;
  border-right: var(--tooltip-arrow-size, 5px) solid transparent;
  border-top: var(--tooltip-arrow-size, 5px) solid var(--tooltip-bg, #000000);
}
.listing-badge.ordered-colors-badge {
  position: relative;
}

/* Fit Types, ASIN, Published Date, Amazon Choice, Total Sold/Returned */
.fit-types-badge,
.Asin-badge,
.published-date-badge,
.amazon-choice-badge,
.total-sold-returned-badge {
  background: linear-gradient(90deg, rgba(96,111,149,0) 0%, rgba(96,111,149,0.10) 100%) !important;
  border: 1.3px solid rgba(96,111,149,0.2) !important;
  color: #606F95 !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.fit-types-badge img,
.Asin-badge img,
.published-date-badge img,
.amazon-choice-badge img,
.total-sold-returned-badge img {
  color: #606F95 !important;
  fill: #606F95 !important;
  filter: none !important;
}

/* Amazon Choice Badge - Black icon in light mode only */
:root .amazon-choice-badge img {
  color: #000000 !important;
  fill: #000000 !important;
  filter: brightness(0) !important;
}

/* Amazon Choice Badge - Keep gray icon in dark mode */
[data-theme="dark"] .amazon-choice-badge img {
  color: #606F95 !important;
  fill: #606F95 !important;
  filter: none !important;
}

/* BSR Badge */
.BSR-badge {
  background: #470CED !important;
  color: #fff !important;
  border-radius: 6px !important;
  font-weight: 500;
  border: none !important;
}

/* Last Month Sold Units Badge */
.last-month-sold-units-badge {
  background: #6F00FF !important;
  color: #fff !important;
  border-radius: 6px !important;
  font-weight: 500;
  border: none !important;
}

/* Hot Seller, Rating Badge */
.hot-seller-badge,
.Rating-badge {
  background: linear-gradient(90deg, rgba(253,89,0,0) 0%, rgba(253,89,0,0.10) 100%) !important;
  border: 1.3px solid rgba(253,89,0,0.2) !important;
  color: #FD5900 !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.hot-seller-badge img,
.Rating-badge img {
  color: #FD5900 !important;
  fill: #FD5900 !important;
  filter: none !important;
}

/* Sale Badge, Top Seller Badge */
.sale-badge,
.top-seller-badge {
  background: linear-gradient(90deg, rgba(255,57,31,0) 0%, rgba(255,57,31,0.10) 100%) !important;
  border: 1.3px solid rgba(255,57,31,0.2) !important;
  color: #FF391F !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.sale-badge img,
.top-seller-badge img {
  color: #FF391F !important;
  fill: #FF391F !important;
  filter: none !important;
}

/* Instant Seller Badge */
.instant-seller-badge {
  background: linear-gradient(90deg, rgba(255,149,0,0) 0%, rgba(255,149,0,0.10) 100%) !important;
  border: 1.3px solid rgba(255,149,0,0.2) !important;
  color: #FF9500 !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.instant-seller-badge img {
  color: #FF9500 !important;
  fill: #FF9500 !important;
  filter: none !important;
}

/* Blazing Seller Badge */
.blazing-seller-badge {
  background: linear-gradient(90deg, rgba(4,174,44,0) 0%, rgba(4,174,44,0.10) 100%) !important;
  border: 1.3px solid rgba(4,174,44,0.2) !important;
  color: #04AE2C !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.blazing-seller-badge img {
  color: #04AE2C !important;
  fill: #04AE2C !important;
  filter: none !important;
}

/* Fresh Seller Badge */
.fresh-seller-badge {
  background: linear-gradient(90deg, rgba(15,188,249,0) 0%, rgba(15,188,249,0.10) 100%) !important;
  border: 1.3px solid rgba(15,188,249,0.2) !important;
  color: #0FBCF9 !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.fresh-seller-badge img {
  color: #0FBCF9 !important;
  fill: #0FBCF9 !important;
  filter: none !important;
}

/* Last Sold Badge */
.last-sold-badge {
  background: linear-gradient(90deg, rgba(0,171,190,0) 0%, rgba(0,171,190,0.10) 100%) !important;
  border: 1.3px solid rgba(0,171,190,0.2) !important;
  color: #00ABBE !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.last-sold-badge img {
  color: #00ABBE !important;
  fill: #00ABBE !important;
  filter: none !important;
}

/* Sold Time Badge, Competing Listings Badge */
.sold-time-badge,
.competing-lisitngs-badge {
  background: linear-gradient(90deg, rgba(4,174,44,0) 0%, rgba(4,174,44,0.10) 100%) !important;
  border: 1.3px solid rgba(4,174,44,0.2) !important;
  color: #04AE2C !important;
  border-radius: 6px !important;
  font-weight: 500;
}
.sold-time-badge img,
.competing-lisitngs-badge img {
  color: #04AE2C !important;
  fill: #04AE2C !important;
  filter: none !important;
}

/* --- END BADGE GRADIENTS --- */

.competing-lisitngs-badge {
  background: #04AE2C !important;
  color: #fff !important;
  border-radius: 6px !important;
  font-weight: 500;
  border: none !important;
  height: 24px !important;
  min-height: 24px !important;
  max-height: 24px !important;
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  padding: 0 10px !important;
}
.competing-lisitngs-badge img {
  color: #fff !important;
  fill: #fff !important;
  filter: none !important;
  width: 14px !important;
  height: 12px !important;
  min-width: 14px !important;
  min-height: 12px !important;
  max-width: 14px !important;
  max-height: 12px !important;
  margin-right: 0 !important;
  margin-left: 0 !important;
  vertical-align: middle !important;
}

/* Hide canceled-units-badge when value is 0 */
.canceled-units-badge:has(span:last-child:is([data-value="0"], :contains("0"))) {
  display: none !important;
}

/* Hide returned-units-badge when value is 0 or -0 */
.returned-units-badge:has(span:last-child:is([data-value="0"], [data-value="-0"], :contains("0"), :contains("-0"))) {
  display: none !important;
}

/* Alternative approach using CSS attribute selectors for better browser support */
.canceled-units-badge span:last-child[data-value="0"],
.returned-units-badge span:last-child[data-value="0"],
.returned-units-badge span:last-child[data-value="-0"] {
  display: none !important;
}

.canceled-units-badge:has(span:last-child[data-value="0"]),
.returned-units-badge:has(span:last-child[data-value="0"]),
.returned-units-badge:has(span:last-child[data-value="-0"]) {
  display: none !important;
}

/* Search No Results State */
.search-no-results {
  width: 100%;
  display: none;
  box-sizing: border-box;
  /* Center within available space below marketplaces */
  flex: 1; /* Take up remaining vertical space */
  min-height: 200px; /* Minimum height to ensure visibility */
  margin-top: 24px; /* Space from marketplaces-sales-row */
  padding: 0; /* Remove fixed padding */
}

/* Hide listing dividers when no results state is active */
.search-no-results:not([style*="display: none"]) ~ .listing-section-divider,
.search-no-results[style*="display: block"] ~ .listing-section-divider {
  display: none !important;
}

.no-results-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  width: 100%;
  height: 100%; /* Fill the parent container */
}

.no-results-image {
  width: 104px; /* 200% of original 52px */
  height: 104px; /* 200% of original 52px */
}

.no-results-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.no-results-message {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.2;
  color: var(--text-primary);
  text-align: center;
}

.search-term-display {
  font-weight: 700;
  color: var(--text-accent);
}

/* Dark theme support for no results */
[data-theme="dark"] .no-results-message {
  color: var(--text-primary);
}

[data-theme="dark"] .search-term-display {
  color: var(--text-accent);
}

/* Sales Section Divider */
.sales-section-divider {
  width: 100%;
  height: 1.5px;
  background: rgba(232, 235, 244, 0.5);
  border: none;
  margin: 24px 0 24px 0;
  flex-shrink: 0;
}
[data-theme="dark"] .sales-section-divider {
  background: #B4B9C5;
  opacity: 0.1;
}

/* Listing Section Divider */
.listing-section-divider {
  width: 100%;
  height: 1.5px;
  background: rgba(232, 235, 244, 0.5);
  border: none;
  margin: 24px 0 0 0;
  flex-shrink: 0;
  /* Smooth transitions for divider visibility changes */
  transition: opacity 0.15s ease-in-out;
}
[data-theme="dark"] .listing-section-divider {
  background: rgba(255, 255, 255, 0.05);
}

/* No Sales State Styles (Figma accurate) */
.no-sales-state {
  display: none; /* Initially hidden */
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
  /* Center within available space below marketplaces */
  flex: 1; /* Take up remaining vertical space */
  min-height: 200px; /* Minimum height to ensure visibility */
  margin-top: 24px; /* Space from marketplaces-sales-row */
  padding: 0; /* Remove fixed padding */
}

.no-sales-img {
  width: 98.18px;
  height: 108.66px;
  flex-shrink: 0;
}

.no-sales-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
}

.no-sales-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 1.197;
  color: var(--text-primary);
  margin: 0;
}

.no-sales-subtitle {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.197;
  color: var(--text-primary);
  margin: 0;
}

/* Dark theme support for no-sales state */
[data-theme="dark"] .no-sales-title,
[data-theme="dark"] .no-sales-subtitle {
  color: var(--text-primary);
}

/* ============================================================================
   LAST WEEK'S SALES CARD STYLES
   ============================================================================ */

/* Last Week's Sales Card - Full Width */
.last-week-sales-card {
  width: 100%;
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color);
  border-radius: 14px;
  padding: 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  transition: var(--theme-transition);
}

[data-theme="dark"] .last-week-sales-card {
  background: var(--bg-primary);
  border-color: var(--border-color);
}

/* Today vs Previous Years Sales Card - Full Width */
.today-vs-previous-years-card {
  width: 100%;
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color);
  border-radius: 14px;
  padding: 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  transition: var(--theme-transition);
}

[data-theme="dark"] .today-vs-previous-years-card {
  background: var(--bg-primary);
  border-color: var(--border-color);
}

/* Header Layout - Title/Date on left, Controls on right */
.last-week-sales-card .Sales-title-date-div,
.today-vs-previous-years-card .Sales-title-date-div {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0px;
  width: 100%;
}

.last-week-sales-card .title-date-section,
.today-vs-previous-years-card .title-date-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.last-week-sales-card .title-date-text {
  display: flex;
  align-items: center;
  gap: 10px;
}

.last-week-sales-card .controls-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Compare Button */
.last-week-sales-card .compare-div {
  display: flex;
  align-items: center;
  position: relative; /* For dropdown positioning */
}

.last-week-sales-card .compare-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #F7F8FA;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.last-week-sales-card .compare-btn:hover {
  background: #E5E7EB;
}

/* Compare button active state (when compare mode is enabled) */
.last-week-sales-card .compare-btn.active {
  background: #470CED !important;
}

.last-week-sales-card .compare-btn.active img {
  filter: brightness(0) invert(1); /* Make icon white */
}

[data-theme="dark"] .last-week-sales-card .compare-btn {
  background: #292E38; /* Match listing-edit-ic and sales-filter-div background */
}

[data-theme="dark"] .last-week-sales-card .compare-btn:hover {
  background: #3A4048;
}

/* Compare Dropdown Menu - Matching marketplace dropdown styling */
.last-week-sales-card .compare-dropdown {
  position: absolute;
  top: calc(100% + 20px); /* 20px gap below the button */
  left: 50%;
  transform: translateX(-50%); /* Center horizontally */
  width: auto; /* Auto-fit content */
  min-width: 200px; /* Minimum width */
  background: #FFFFFF;
  border: 1.5px solid #E9EBF2; /* Match marketplace dropdown border */
  border-radius: 8px; /* Match marketplace dropdown border radius */
  z-index: 1000;
  display: none; /* Hidden by default */
  box-sizing: border-box;
  opacity: 0;
  transform: translateX(-50%) translateY(-10px);
  transition: all 0.2s ease;
}

[data-theme="dark"] .last-week-sales-card .compare-dropdown {
  background: var(--bg-primary); /* Match marketplace dropdown dark background */
  border: 1.5px solid var(--border-color) !important; /* Match marketplace dropdown dark border */
}

.last-week-sales-card .compare-dropdown.show {
  display: block;
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.last-week-sales-card .compare-dropdown-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px; /* Match marketplace dropdown item padding */
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap; /* Prevent text wrapping */
  font-size: 12px; /* Match marketplace dropdown font size */
}

.last-week-sales-card .compare-dropdown-item:hover {
  background: #F3F4F6; /* Match marketplace dropdown hover color */
}

.last-week-sales-card .compare-dropdown-item:first-child:hover {
  border-top-left-radius: 5px; /* Match marketplace dropdown corner radius */
  border-top-right-radius: 5px;
}

.last-week-sales-card .compare-dropdown-item:last-child:hover {
  border-bottom-left-radius: 5px; /* Match marketplace dropdown corner radius */
  border-bottom-right-radius: 5px;
}

[data-theme="dark"] .last-week-sales-card .compare-dropdown-item:hover,
[data-theme="dark"] .last-week-sales-card .compare-dropdown-item.selected {
  background: #292E38 !important; /* Match marketplace dropdown dark hover */
  color: var(--text-accent) !important;
}

[data-theme="dark"] .last-week-sales-card .compare-dropdown-item:first-child:hover,
[data-theme="dark"] .last-week-sales-card .compare-dropdown-item:first-child.selected {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

[data-theme="dark"] .last-week-sales-card .compare-dropdown-item:last-child:hover,
[data-theme="dark"] .last-week-sales-card .compare-dropdown-item:last-child.selected {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.last-week-sales-card .compare-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px; /* Increased from 14px to 20px */
  height: 20px; /* Increased from 14px to 20px */
  flex-shrink: 0;
}

.last-week-sales-card .compare-checkbox img {
  width: 20px; /* Increased from 14px to 20px */
  height: 20px; /* Increased from 14px to 20px */
  transition: all 0.2s ease;
}

.last-week-sales-card .compare-dropdown-text {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px; /* Match marketplace dropdown font size */
  line-height: 1.2;
  color: #18181B;
  flex: 1;
}

[data-theme="dark"] .last-week-sales-card .compare-dropdown-text {
  color: var(--text-primary) !important; /* Match marketplace dropdown dark text color */
}

/* View Insights Button */
.last-week-sales-card .view-insights-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 10px;
  height: 32px;
  background: #F7F8FA;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: var(--text-primary);
  box-sizing: border-box;
}

.last-week-sales-card .view-insights-btn:hover {
  background: #E5E7EB;
}

[data-theme="dark"] .last-week-sales-card .view-insights-btn {
  background: #292E38; /* Match listing-edit-ic and sales-filter-div background */
  color: var(--text-primary);
}

[data-theme="dark"] .last-week-sales-card .view-insights-btn:hover {
  background: #3A4048;
}

/* Chart Container */
.last-week-chart-container,
.today-vs-previous-years-chart-container {
  width: 100%;
  height: 300px;
  position: relative;
}

/* Marketplace Focus Dropdown Container for Today vs Previous Years Card */
.today-vs-previous-years-card .marketplace-focus-dropdown-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.today-vs-previous-years-card .controls-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .last-week-sales-card .Sales-title-date-div {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .last-week-sales-card .controls-section {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .last-week-sales-card,
  .today-vs-previous-years-card {
    padding: 16px;
  }

  .last-week-chart-container,
  .today-vs-previous-years-chart-container {
    height: 250px;
  }
}

/* Responsive adjustments for no-sales state */
@media (max-width: 600px) {
  .no-sales-state {
    flex-direction: column;
    gap: 12px;
    min-height: 160px; /* Smaller minimum height on mobile */
    margin-top: 16px; /* Reduced margin on mobile */
  }
  
  .no-sales-img {
    width: 80px;
    height: 88px;
  }
  
  .no-sales-text {
    align-items: center;
    text-align: center;
  }
}

/* New Tab Count Styling - Only the number in green */
.new-tab .tab-label .count-green {
  color: #04AE2C !important;
  font-weight: 700 !important;
}

/* New Tab Dimmed State - 50% opacity when no new sellers */
/* Apply opacity to the visible tab content, not the container */
.new-tab.no-new-sellers {
  cursor: not-allowed;
}

.new-tab.no-new-sellers .tab-main,
.new-tab.no-new-sellers .tab-label,
.new-tab.no-new-sellers .tab-icon {
  opacity: 0.5;
}

.new-tab.no-new-sellers .tab-label {
  cursor: not-allowed;
}

/* Dark theme support for new tab count */
[data-theme="dark"] .new-tab .tab-label .count-green {
  color: #04AE2C !important;
}

/* --- BADGE GRADIENTS & STROKES (Figma accurate) --- */

/* Lock Badge - Icon only, red gradient style */
.lock-badge {
  background: linear-gradient(90deg, rgba(255,57,31,0) 0%, rgba(255,57,31,0.10) 100%) !important;
  border: 1.3px solid rgba(255,57,31,0.2) !important;
  color: #FF391F !important;
  border-radius: 6px !important;
  font-weight: 500;
  padding: 0 !important; /* Remove padding for exact sizing */
  width: 20px !important; /* Exact 20px width */
  height: 20px !important; /* Exact 20px height */
  min-width: 20px !important;
  min-height: 20px !important;
  max-width: 20px !important;
  max-height: 20px !important;
  justify-content: center !important;
  display: flex !important;
  align-items: center !important;
}
.lock-badge img {
  width: 12px !important;
  height: 12px !important;
  min-width: 12px !important;
  min-height: 12px !important;
  max-width: 12px !important;
  max-height: 12px !important;
  margin: 0 !important;
  color: #FF391F !important;
  fill: #FF391F !important;
  filter: none !important;
  vertical-align: middle !important;
}

/* Dark theme support for lock badge */
[data-theme="dark"] .lock-badge {
  background: linear-gradient(90deg, rgba(255,57,31,0) 0%, rgba(255,57,31,0.18) 100%) !important;
  border: 1.3px solid rgba(255,57,31,0.3) !important;
  color: #FF391F !important;
}
[data-theme="dark"] .lock-badge img {
  color: #FF391F !important;
  fill: #FF391F !important;
  filter: none !important;
}

/* Lost Royalties Badge */

/* Privacy Mode Styles */
.privacy-mode {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

.privacy-mode-label {
  display: flex;
  align-items: center;
  height: 40px;
  white-space: nowrap;
}

.privacy-mode-label span {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 14px;
  font-weight: 700;
  color: #606F95;
}

.privacy-mode-toggle {
  width: auto !important;
  height: 40px !important;
  min-height: 40px !important;
  max-height: 40px !important;
  padding: 3px !important;
  background: rgba(232, 235, 244, 0.5) !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  gap: 3px !important;
  box-sizing: border-box !important;
}

[data-theme="dark"] .privacy-mode-toggle {
  background: #292E38 !important;
}

.privacy-mode-toggle .off-tab,
.privacy-mode-toggle .on-tab {
  flex: 1;
  height: 100%;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: transparent;
}

.privacy-mode-toggle .off-tab.active,
.privacy-mode-toggle .on-tab.active {
  background: #fff;
  box-shadow: 0 2px 8px rgba(96, 111, 149, 0.04);
}

/* Hover state for inactive privacy toggle tabs only */
.privacy-mode-toggle .off-tab:not(.active):hover,
.privacy-mode-toggle .on-tab:not(.active):hover {
  background: rgba(96, 111, 149, 0.04);
  transition: background 0.2s ease;
}

[data-theme="dark"] .privacy-mode-toggle .off-tab.active,
[data-theme="dark"] .privacy-mode-toggle .on-tab.active {
  background: var(--bg-primary);
  box-shadow: none;
}

/* Dark theme hover state for inactive privacy toggle tabs only */
[data-theme="dark"] .privacy-mode-toggle .off-tab:not(.active):hover,
[data-theme="dark"] .privacy-mode-toggle .on-tab:not(.active):hover {
  background: rgba(255, 255, 255, 0.05);
}

.privacy-mode-toggle .off-div,
.privacy-mode-toggle .on-div {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 16px;
}

.privacy-mode-toggle .off-div img,
.privacy-mode-toggle .on-div img {
  width: 16px;
  height: 16px;
}

.privacy-mode-toggle .off-div span,
.privacy-mode-toggle .on-div span {
  font-family: 'Amazon Ember', sans-serif;
  font-size: 11px;
  font-weight: 500;
  color: var(--text-primary);
  margin-top: 2px;
}

.privacy-mode-toggle .off-tab.active .off-div span {
  color: #470CED;
  font-weight: 700;
}

.privacy-mode-toggle .on-tab.active .on-div span {
  color: #470CED;
  font-weight: 700;
}

[data-theme="dark"] .privacy-mode-label span {
  color: #FFFFFF !important;
}

[data-theme="dark"] .privacy-mode-toggle .off-div span,
[data-theme="dark"] .privacy-mode-toggle .on-div span {
  color: #B4B9C5;
}

/* Dark theme active state text and icon colors for privacy toggle */
[data-theme="dark"] .privacy-mode-toggle .off-tab.active .off-div span,
[data-theme="dark"] .privacy-mode-toggle .on-tab.active .on-div span {
  color: #FFFFFF;
  font-weight: 700;
}

[data-theme="dark"] .privacy-mode-toggle .off-tab.active .off-div img,
[data-theme="dark"] .privacy-mode-toggle .on-tab.active .on-div img {
  filter: brightness(0) invert(1);
}

/* Privacy Mode UI States */
.listing-left-div.state-private {
  background: #470CED;
  position: relative;
  overflow: hidden;
}

.listing-left-div.state-private::before {
  content: '';
  position: absolute;
  top: 12px;
  left: 12px;
  width: 24px;
  height: 24px;
  background-image: url('./assets/privacy-mode-ic.svg');
  background-size: contain;
  background-repeat: no-repeat;
  z-index: 2;
}

.listing-left-div.state-private::after {
  content: 'Privacy Mode';
  position: absolute;
  bottom: 12px;
  left: 12px;
  right: 12px;
  color: #FFFFFF;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.2;
  text-align: left;
  z-index: 2;
}

/* Database right container styles */
.database-right {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: auto;
}

/* ============================================================================
   GLOBAL LOADER SYSTEM
   ============================================================================ */

/* Loader Animation - Core spin animation used across all loaders */
@keyframes snap-loader-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Fade-in animation for smooth loader appearance */
@keyframes snap-loader-fade-in {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

/* ============================================================================
   LOADER VARIANTS
   ============================================================================ */

/* 1. OVERLAY LOADER - Full screen/container overlay with spinner and text */
.snap-loader-overlay {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  width: 100%;
  height: 100%;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box;
  border-radius: inherit;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 10;
  background-color: rgba(255,255,255,0.95);
  font-family: 'Amazon Ember', Arial, sans-serif;
  animation: snap-loader-fade-in 0.2s ease-out;
}
[data-theme="dark"] .snap-loader-overlay {
  background-color: rgba(30,32,40,0.95);
}

/* Overlay Spinner */
.snap-loader-overlay .snap-loader-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(96, 111, 149, 0.1);
  border-radius: 50%;
  border-left-color: #470CED;
  animation: snap-loader-spin 1s linear infinite;
  margin-bottom: 16px;
  box-sizing: border-box;
}

[data-theme="dark"] .snap-loader-overlay .snap-loader-spinner {
  border-color: rgba(180, 185, 197, 0.1);
  border-left-color: #470CED;
}

/* Overlay Text */
.snap-loader-overlay .snap-loader-text {
  font-weight: 500;
  font-size: 16px;
  color: #606F95;
  text-align: center;
  margin: 0;
  line-height: 1.4;
}

[data-theme="dark"] .snap-loader-overlay .snap-loader-text {
  color: #B4B9C5;
}

/* 2. INLINE LOADER - Small loader for inputs and inline elements */
.snap-loader-inline {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 1.5px solid rgba(96, 111, 149, 0.1);
  border-radius: 50%;
  border-left-color: #470CED;
  animation: snap-loader-spin 1s linear infinite;
  pointer-events: none;
  display: none;
  box-sizing: border-box;
}

[data-theme="dark"] .snap-loader-inline {
  border-color: rgba(180, 185, 197, 0.1);
  border-left-color: #470CED;
}

/* Input-specific positioning */
.snap-loader-inline.input-loader {
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
}

/* Button-specific positioning */
.snap-loader-inline.button-loader {
  position: relative;
  display: inline-block;
  margin-right: 8px;
  top: 0;
  transform: none;
}

/* 3. SMALL LOADER - Compact loader for tight spaces */
.snap-loader-small {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(96, 111, 149, 0.1);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: snap-loader-spin 1s linear infinite;
  display: inline-block;
  box-sizing: border-box;
}

[data-theme="dark"] .snap-loader-small {
  border-color: rgba(180, 185, 197, 0.1);
  border-top-color: transparent;
}

/* Small loader with theme-aware accent color */
.snap-loader-small.accent {
  border-left-color: #470CED;
}

[data-theme="dark"] .snap-loader-small.accent {
  border-left-color: #470CED;
}

/* 4. MEDIUM LOADER - Standard size for cards and sections */
.snap-loader-medium {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(96, 111, 149, 0.1);
  border-radius: 50%;
  border-left-color: #470CED;
  animation: snap-loader-spin 1s linear infinite;
  display: inline-block;
  box-sizing: border-box;
}

[data-theme="dark"] .snap-loader-medium {
  border-color: rgba(180, 185, 197, 0.1);
  border-left-color: #470CED;
}

/* 5. LARGE LOADER - For major loading operations */
.snap-loader-large {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(96, 111, 149, 0.1);
  border-radius: 50%;
  border-left-color: #470CED;
  animation: snap-loader-spin 1s linear infinite;
  display: inline-block;
  box-sizing: border-box;
}

[data-theme="dark"] .snap-loader-large {
  border-color: rgba(180, 185, 197, 0.1);
  border-left-color: #470CED;
}

/* ============================================================================
   LOADER STATES & MODIFIERS
   ============================================================================ */

/* Success state - green accent */
.snap-loader-success .snap-loader-spinner,
.snap-loader-success.snap-loader-inline,
.snap-loader-success.snap-loader-small,
.snap-loader-success.snap-loader-medium,
.snap-loader-success.snap-loader-large {
  border-left-color: #04AE2C;
}

/* Error state - red accent */
.snap-loader-error .snap-loader-spinner,
.snap-loader-error.snap-loader-inline,
.snap-loader-error.snap-loader-small,
.snap-loader-error.snap-loader-medium,
.snap-loader-error.snap-loader-large {
  border-left-color: #FF391F;
}

/* Warning state - orange accent */
.snap-loader-warning .snap-loader-spinner,
.snap-loader-warning.snap-loader-inline,
.snap-loader-warning.snap-loader-small,
.snap-loader-warning.snap-loader-medium,
.snap-loader-warning.snap-loader-large {
  border-left-color: #F77F16;
}

/* Slow animation for long operations */
.snap-loader-slow .snap-loader-spinner,
.snap-loader-slow.snap-loader-inline,
.snap-loader-slow.snap-loader-small,
.snap-loader-slow.snap-loader-medium,
.snap-loader-slow.snap-loader-large {
  animation-duration: 2s;
}

/* Fast animation for quick operations */
.snap-loader-fast .snap-loader-spinner,
.snap-loader-fast.snap-loader-inline,
.snap-loader-fast.snap-loader-small,
.snap-loader-fast.snap-loader-medium,
.snap-loader-fast.snap-loader-large {
  animation-duration: 0.5s;
}

/* ============================================================================
   LOADER UTILITIES
   ============================================================================ */

/* Hide element while showing loader */
.snap-loader-hidden {
  opacity: 0;
  pointer-events: none;
}

/* Container with relative positioning for absolute loaders */
.snap-loader-container {
  position: relative;
}

/* Disable interactions while loading */
.snap-loader-disabled {
  pointer-events: none;
  opacity: 0.6;
  cursor: not-allowed;
}

/* Button loading state */
.snap-loader-button-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.8;
}

.snap-loader-button-loading::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-left-color: #FFFFFF;
  animation: snap-loader-spin 1s linear infinite;
  z-index: 1;
}

/* Dark theme button loading */
[data-theme="dark"] .snap-loader-button-loading::before {
  border-color: rgba(180, 185, 197, 0.3);
  border-left-color: #FFFFFF;
}

/* ============================================================================
   COMPONENT INTEGRATION
   ============================================================================ */

/* Sales card loading state */
.Sales-card-div.loading {
  position: relative;
}

.Sales-card-div.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

[data-theme="dark"] .Sales-card-div.loading::after {
  background: rgba(30, 32, 40, 0.9);
}

/* Sidebar button loading state */
.sidebar-btn.loading .sidebar-icon {
  opacity: 0.5;
}

.sidebar-btn.loading::after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  border: 1.5px solid rgba(96, 111, 149, 0.2);
  border-radius: 50%;
  border-left-color: #470CED;
  animation: snap-loader-spin 1s linear infinite;
}

[data-theme="dark"] .sidebar-btn.loading::after {
  border-color: rgba(180, 185, 197, 0.2);
  border-left-color: #470CED;
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 600px) {
  /* Smaller overlay spinner on mobile */
  .snap-loader-overlay .snap-loader-spinner {
    width: 32px;
    height: 32px;
    border-width: 3px;
    margin-bottom: 12px;
  }
  
  /* Smaller overlay text on mobile */
  .snap-loader-overlay .snap-loader-text {
    font-size: 14px;
  }
  
  /* Adjust large loader for mobile */
  .snap-loader-large {
    width: 36px;
    height: 36px;
    border-width: 3px;
  }
}

/* ============================================================================
   ACCESSIBILITY
   ============================================================================ */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .snap-loader-spinner,
  .snap-loader-inline,
  .snap-loader-small,
  .snap-loader-medium,
  .snap-loader-large,
  .snap-loader-button-loading::before,
  .sidebar-btn.loading::after {
    animation-duration: 2s;
  }
}

/* Screen reader support */
.snap-loader-overlay[aria-label]::after,
.snap-loader-inline[aria-label]::after,
.snap-loader-small[aria-label]::after,
.snap-loader-medium[aria-label]::after,
.snap-loader-large[aria-label]::after {
  content: attr(aria-label);
  position: absolute;
  left: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Focus management during loading */
.snap-loader-overlay:focus,
.snap-loader-container:focus-within .snap-loader-overlay {
  outline: 2px solid #470CED;
  outline-offset: 2px;
}

/* Compact loader overlay for small containers (e.g., ad-spend) */
.snap-loader-overlay.snap-loader-compact {
  padding: 12px 0;
}
.snap-loader-overlay.snap-loader-compact .snap-loader-spinner {
  /* Do not override width/height, use default (medium) */
  margin-bottom: 8px;
}
.snap-loader-overlay.snap-loader-compact .snap-loader-text {
  font-size: 13px;
  font-weight: 400;
  margin: 0;
  padding: 0;
}

/* Loader overlay should match ad-spend container shape and size */
.ad-spend > .snap-loader-overlay {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  width: 100%;
  height: 100%;
  border-radius: 14px;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 10;
  background-color: rgba(255,255,255,0.95);
  /* Inherit font and transitions */
  font-family: 'Amazon Ember', Arial, sans-serif;
  animation: snap-loader-fade-in 0.2s ease-out;
}
[data-theme="dark"] .ad-spend > .snap-loader-overlay {
  background-color: rgba(30,32,40,0.95);
}

.snap-loader-overlay,
.ad-spend > .snap-loader-overlay {
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box;
}

.drag-drop-area,
.tab,
.tab-container {
  /* transition: var(--theme-transition); */
}

.drag-drop-area:hover,
.tab:hover,
.tab-container:hover {
  transition: background-color 0.3s ease;
}

.loaded-files-container,
.loaded-files-container.visible,
.processing-files-container {
  /* Removed transition: var(--theme-transition); */
}

/* If you want transitions on hover/feedback, add them only to those states, e.g.: */
.loaded-files-container:hover,
.processing-files-container:hover {
  transition: background 0.2s ease, border-color 0.2s ease;
}

/* Hide listing-ad-row when it has ad-no-sales class or contains zero values */
.listing-ad-row.ad-no-sales {
  display: none !important;
}

/* Hide listing-ad-row when ad spend shows zero values or no sales */
.listing-ad-row:has(.listing-ad-label:contains("$0")),
.listing-ad-row:has(.listing-ad-label:contains("€0")),
.listing-ad-row:has(.listing-ad-label:contains("£0")),
.listing-ad-row:has(.listing-ad-label:contains("¥0")),
.listing-ad-row:has(.listing-ad-label:contains("(0)")),
.listing-ad-row:has(.listing-ad-label:contains("0.0")),
.listing-ad-row:has(.listing-ad-label:contains("0,0")) {
  display: none !important;
}

